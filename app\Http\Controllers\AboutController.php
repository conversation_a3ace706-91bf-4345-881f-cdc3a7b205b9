<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class AboutController extends Controller
{
    public function index()
    {
        $companyInfo = [
            'founded' => '2018',
            'clients' => '200+',
            'projects' => '500+',
            'team_size' => '25+',
            'experience' => '6+'
        ];

        $team = [
            [
                'name' => session('locale', 'ar') === 'ar' ? 'أحمد محمد' : '<PERSON>',
                'position' => session('locale', 'ar') === 'ar' ? 'المدير التنفيذي ومؤسس الشركة' : 'CEO & Founder',
                'bio' => session('locale', 'ar') === 'ar' ? 'خبرة أكثر من 10 سنوات في مجال التسويق الرقمي وإدارة الأعمال' : 'Over 10 years of experience in digital marketing and business management',
                'image' => 'team-1.jpg',
                'social' => [
                    'linkedin' => '#',
                    'twitter' => '#'
                ]
            ],
            [
                'name' => session('locale', 'ar') === 'ar' ? 'سارة أحمد' : '<PERSON>',
                'position' => session('locale', 'ar') === 'ar' ? 'مديرة التسويق الرقمي' : 'Digital Marketing Manager',
                'bio' => session('locale', 'ar') === 'ar' ? 'متخصصة في إدارة الحملات الإعلانية ووسائل التواصل الاجتماعي' : 'Specialist in advertising campaigns and social media management',
                'image' => 'team-2.jpg',
                'social' => [
                    'linkedin' => '#',
                    'instagram' => '#'
                ]
            ],
            [
                'name' => session('locale', 'ar') === 'ar' ? 'محمد علي' : 'Mohamed Ali',
                'position' => session('locale', 'ar') === 'ar' ? 'مطور مواقع إلكترونية' : 'Web Developer',
                'bio' => session('locale', 'ar') === 'ar' ? 'خبير في تطوير المواقع الإلكترونية وتحسين محركات البحث' : 'Expert in web development and search engine optimization',
                'image' => 'team-3.jpg',
                'social' => [
                    'linkedin' => '#',
                    'github' => '#'
                ]
            ],
            [
                'name' => session('locale', 'ar') === 'ar' ? 'فاطمة حسن' : 'Fatima Hassan',
                'position' => session('locale', 'ar') === 'ar' ? 'مصممة جرافيك' : 'Graphic Designer',
                'bio' => session('locale', 'ar') === 'ar' ? 'مبدعة في تصميم الهويات البصرية والمحتوى الإبداعي' : 'Creative in visual identity design and creative content',
                'image' => 'team-4.jpg',
                'social' => [
                    'behance' => '#',
                    'instagram' => '#'
                ]
            ]
        ];

        $values = [
            [
                'title' => session('locale', 'ar') === 'ar' ? 'الإبداع والابتكار' : 'Creativity & Innovation',
                'description' => session('locale', 'ar') === 'ar' ? 'نؤمن بقوة الأفكار الإبداعية في تحقيق نتائج استثنائية' : 'We believe in the power of creative ideas to achieve exceptional results',
                'icon' => 'fas fa-lightbulb',
                'color' => 'warning'
            ],
            [
                'title' => session('locale', 'ar') === 'ar' ? 'الشفافية والثقة' : 'Transparency & Trust',
                'description' => session('locale', 'ar') === 'ar' ? 'نبني علاقات طويلة الأمد مع عملائنا على أساس الثقة والشفافية' : 'We build long-term relationships with our clients based on trust and transparency',
                'icon' => 'fas fa-handshake',
                'color' => 'success'
            ],
            [
                'title' => session('locale', 'ar') === 'ar' ? 'التميز في الأداء' : 'Excellence in Performance',
                'description' => session('locale', 'ar') === 'ar' ? 'نسعى دائماً لتقديم أفضل النتائج وتجاوز توقعات عملائنا' : 'We always strive to deliver the best results and exceed our clients\' expectations',
                'icon' => 'fas fa-trophy',
                'color' => 'primary'
            ],
            [
                'title' => session('locale', 'ar') === 'ar' ? 'التطوير المستمر' : 'Continuous Development',
                'description' => session('locale', 'ar') === 'ar' ? 'نواكب أحدث التطورات في عالم التسويق الرقمي لنقدم أفضل الحلول' : 'We keep up with the latest developments in digital marketing to provide the best solutions',
                'icon' => 'fas fa-chart-line',
                'color' => 'info'
            ]
        ];

        $milestones = [
            [
                'year' => '2018',
                'title' => session('locale', 'ar') === 'ar' ? 'تأسيس الشركة' : 'Company Founded',
                'description' => session('locale', 'ar') === 'ar' ? 'بدأنا رحلتنا بفريق صغير وحلم كبير' : 'We started our journey with a small team and a big dream'
            ],
            [
                'year' => '2019',
                'title' => session('locale', 'ar') === 'ar' ? 'أول 50 عميل' : 'First 50 Clients',
                'description' => session('locale', 'ar') === 'ar' ? 'حققنا ثقة أول 50 عميل وبدأنا في التوسع' : 'We gained the trust of our first 50 clients and started expanding'
            ],
            [
                'year' => '2021',
                'title' => session('locale', 'ar') === 'ar' ? 'توسع الفريق' : 'Team Expansion',
                'description' => session('locale', 'ar') === 'ar' ? 'نمو الفريق إلى 15 متخصص في مختلف المجالات' : 'Team grew to 15 specialists in various fields'
            ],
            [
                'year' => '2023',
                'title' => session('locale', 'ar') === 'ar' ? 'أكثر من 200 عميل' : 'Over 200 Clients',
                'description' => session('locale', 'ar') === 'ar' ? 'وصلنا إلى أكثر من 200 عميل راضي عن خدماتنا' : 'We reached over 200 satisfied clients with our services'
            ]
        ];

        return view('about', compact('companyInfo', 'team', 'values', 'milestones'));
    }
}
