<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Collection;

class AdminController extends Controller
{
    public function __construct()
    {
        // Check admin authentication for all methods
        $this->middleware(function ($request, $next) {
            if (!Session::get('admin_logged_in')) {
                return redirect()->route('admin.login');
            }
            return $next($request);
        });
    }

    public function dashboard()
    {
        $stats = [
            'total_services' => 8,
            'total_blog_posts' => 6,
            'total_faqs' => 20,
            'total_testimonials' => 12,
            'monthly_visitors' => 2450,
            'contact_submissions' => 45,
            'quote_requests' => 23,
            'newsletter_subscribers' => 156
        ];

        $recentActivities = [
            [
                'type' => 'contact',
                'message' => 'New contact form submission from <PERSON>',
                'time' => '2 hours ago',
                'icon' => 'fas fa-envelope'
            ],
            [
                'type' => 'quote',
                'message' => 'Quote request for Social Media Management',
                'time' => '4 hours ago',
                'icon' => 'fas fa-file-invoice'
            ],
            [
                'type' => 'newsletter',
                'message' => '3 new newsletter subscriptions',
                'time' => '6 hours ago',
                'icon' => 'fas fa-users'
            ],
            [
                'type' => 'blog',
                'message' => 'Blog post "Digital Marketing Tips" viewed 50 times',
                'time' => '1 day ago',
                'icon' => 'fas fa-eye'
            ]
        ];

        return view('admin.dashboard', compact('stats', 'recentActivities'));
    }

    // Blog Management
    public function blogIndex()
    {
        $articles = $this->getBlogArticles();
        return view('admin.blog.index', compact('articles'));
    }

    public function blogCreate()
    {
        $categories = $this->getBlogCategories();
        return view('admin.blog.create', compact('categories'));
    }

    public function blogStore(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'excerpt' => 'required|string',
            'content' => 'required|string',
            'category' => 'required|string',
            'tags' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        // In a real application, save to database
        // For now, we'll simulate success
        return redirect()->route('admin.blog.index')->with('success', 'Article created successfully');
    }

    public function blogEdit($id)
    {
        $article = $this->getBlogArticleById($id);
        $categories = $this->getBlogCategories();
        return view('admin.blog.edit', compact('article', 'categories'));
    }

    public function blogUpdate(Request $request, $id)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'excerpt' => 'required|string',
            'content' => 'required|string',
            'category' => 'required|string',
            'tags' => 'required|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        // In a real application, update in database
        return redirect()->route('admin.blog.index')->with('success', 'Article updated successfully');
    }

    public function blogDestroy($id)
    {
        // In a real application, delete from database
        return redirect()->route('admin.blog.index')->with('success', 'تم حذف المقال بنجاح');
    }

    // FAQ Management
    public function faqIndex()
    {
        $faqs = $this->getFaqs();
        return view('admin.faq.index', compact('faqs'));
    }

    public function faqCreate()
    {
        $categories = ['general', 'services', 'pricing', 'process', 'support'];
        return view('admin.faq.create', compact('categories'));
    }

    public function faqStore(Request $request)
    {
        $request->validate([
            'question' => 'required|string|max:500',
            'answer' => 'required|string',
            'category' => 'required|string',
            'order' => 'nullable|integer|min:0'
        ]);

        // In a real application, save to database
        $action = $request->input('action', 'save');
        
        if ($action === 'save_and_continue') {
            return redirect()->route('admin.faq.create')->with('success', 'FAQ created successfully. You can add another FAQ.');
        }
        
        return redirect()->route('admin.faq.index')->with('success', 'FAQ created successfully');
    }
    
    public function faqEdit($id)
    {
        $faq = collect($this->getFaqs())->firstWhere('id', $id);
        if (!$faq) {
            return redirect()->route('admin.faq.index')->with('error', 'FAQ not found');
        }
        return view('admin.faq.edit', compact('faq'));
    }
    
    public function faqUpdate(Request $request, $id)
    {
        $request->validate([
            'question' => 'required|string|max:500',
            'answer' => 'required|string',
            'category' => 'required|string',
            'order' => 'nullable|integer|min:0'
        ]);

        // In a real application, update in database
        return redirect()->route('admin.faq.index')->with('success', 'FAQ updated successfully');
    }
    
    public function faqDestroy($id)
    {
        // In a real application, delete from database
        return redirect()->route('admin.faq.index')->with('success', 'FAQ deleted successfully');
    }
    
    public function faqBulkAction(Request $request)
    {
        $action = $request->input('bulk_action');
        $selectedIds = $request->input('selected_faqs', []);
        
        if (empty($selectedIds)) {
            return redirect()->route('admin.faq.index')->with('error', 'Please select at least one item');
        }
        
        $count = count($selectedIds);
        
        switch ($action) {
            case 'activate':
                // In a real application, update status in database
                return redirect()->route('admin.faq.index')->with('success', "{$count} FAQ(s) activated successfully");
            case 'deactivate':
                return redirect()->route('admin.faq.index')->with('success', "{$count} FAQ(s) deactivated successfully");
            case 'delete':
                return redirect()->route('admin.faq.index')->with('success', "{$count} FAQ(s) deleted successfully");
            default:
                return redirect()->route('admin.faq.index')->with('error', 'Invalid action');
        }
    }
    
    public function faqToggleStatus($id)
    {
        // In a real application, toggle status in database
        return response()->json([
            'success' => true,
            'message' => 'FAQ status updated successfully'
        ]);
    }

    // Contact & Quote Management
    public function contactsIndex()
    {
        $contacts = $this->getContactSubmissions();
        return view('admin.contacts.index', compact('contacts'));
    }
    
    public function contactsShow($id)
    {
        $contact = collect($this->getContactSubmissions())->firstWhere('id', $id);
        if (!$contact) {
            return redirect()->route('admin.contacts.index')->with('error', 'الرسالة غير موجودة');
        }
        return view('admin.contacts.show', compact('contact'));
    }
    
    public function contactsDestroy($id)
    {
        // In a real application, delete from database
        return redirect()->route('admin.contacts.index')->with('success', 'تم حذف الرسالة بنجاح');
    }
    
    public function contactsMarkRead($id)
    {
        // In a real application, update status in database
        return response()->json([
            'success' => true,
            'message' => 'تم تحديد الرسالة كمقروءة'
        ]);
    }

    public function quotesIndex()
    {
        $quotes = $this->getQuoteRequests();
        return view('admin.quotes.index', compact('quotes'));
    }
    
    public function quotesShow($id)
    {
        $quote = collect($this->getQuoteRequests())->firstWhere('id', $id);
        if (!$quote) {
            return redirect()->route('admin.quotes.index')->with('error', 'طلب العرض غير موجود');
        }
        return view('admin.quotes.show', compact('quote'));
    }
    
    public function quotesDestroy($id)
    {
        // In a real application, delete from database
        return redirect()->route('admin.quotes.index')->with('success', 'تم حذف طلب العرض بنجاح');
    }
    
    public function quotesUpdateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,sent,accepted,rejected'
        ]);
        
        // In a real application, update status in database
        return response()->json([
            'success' => true,
            'message' => 'تم تحديث حالة طلب العرض بنجاح'
        ]);
    }
    
    public function quotesSend(Request $request, $id)
    {
        $request->validate([
            'quote_price' => 'required|numeric|min:0',
            'delivery_time' => 'required|string',
            'quote_details' => 'required|string|min:10',
            'terms_conditions' => 'nullable|string'
        ]);

        // In a real application, this would:
        // 1. Find the quote in database
        // 2. Create a quote response record
        // 3. Send email to client
        // 4. Update quote status to 'sent'
        
        return redirect()->route('admin.quotes.show', $id)
            ->with('success', 'تم إرسال العرض بنجاح للعميل');
    }

    // Settings
    public function settings()
    {
        $settings = [
            'site_name_ar' => 'إيجنت ماركتنج',
            'site_name_en' => 'Agent Marketing',
            'contact_email' => '<EMAIL>',
            'contact_phone' => '+966 50 123 4567',
            'whatsapp_number' => '+966 50 123 4567',
            'facebook_url' => 'https://facebook.com/agentmarketing',
            'twitter_url' => 'https://twitter.com/agentmarketing',
            'instagram_url' => 'https://instagram.com/agentmarketing',
            'linkedin_url' => 'https://linkedin.com/company/agentmarketing'
        ];
        
        return view('admin.settings', compact('settings'));
    }

    public function updateSettings(Request $request)
    {
        $request->validate([
            'site_name_ar' => 'required|string|max:255',
            'site_name_en' => 'required|string|max:255',
            'contact_email' => 'required|email',
            'contact_phone' => 'required|string',
            'whatsapp_number' => 'required|string'
        ]);

        // In a real application, save to database or config
        return redirect()->route('admin.settings.index')->with('success', 'تم تحديث الإعدادات بنجاح');
    }

    // Helper methods (in production, these would interact with database)
    private function getBlogArticles()
    {
        return [
            [
                'id' => 1,
                'title' => 'Modern Digital Marketing Strategies',
                'category' => 'digital-marketing',
                'status' => 'published',
                'views' => 245,
                'created_at' => '2024-01-15',
                'featured' => true
            ],
            [
                'id' => 2,
                'title' => 'How to Optimize Advertising Campaigns',
                'category' => 'advertising',
                'status' => 'published',
                'views' => 189,
                'created_at' => '2024-01-10',
                'featured' => false
            ],
            [
                'id' => 3,
                'title' => 'The Importance of Social Media Marketing',
                'category' => 'social-media',
                'status' => 'draft',
                'views' => 0,
                'created_at' => '2024-01-08',
                'featured' => false
            ]
        ];
    }

    private function getBlogArticleById($id)
    {
        $articles = $this->getBlogArticles();
        $collection = new Collection($articles);
        return $collection->firstWhere('id', $id);
    }

    private function getBlogCategories()
    {
        return [
            'digital-marketing' => 'Digital Marketing',
            'social-media' => 'Social Media',
            'seo' => 'Search Engine Optimization',
            'content-marketing' => 'Content Marketing',
            'advertising' => 'Advertising',
            'tips' => 'Tips & Tricks'
        ];
    }

    private function getFaqs()
    {
        return [
            [
                'id' => 1,
                'question' => 'What digital marketing services do you offer?',
                'answer' => 'We offer a comprehensive range of digital marketing services including social media management, paid advertising, search engine optimization, and content marketing.',
                'category' => 'services',
                'status' => 'active',
                'views' => 156,
                'order' => 1,
                'created_at' => '2024-01-01'
            ],
            [
                'id' => 2,
                'question' => 'How long does a marketing campaign take to show results?',
                'answer' => 'The duration varies depending on the type of campaign and objectives, but we typically start seeing initial results within 2-4 weeks, and tangible results within 2-3 months.',
                'category' => 'process',
                'status' => 'active',
                'views' => 89,
                'order' => 2,
                'created_at' => '2024-01-02'
            ],
            [
                'id' => 3,
                'question' => 'Do you provide website design services?',
                'answer' => 'Yes, we provide responsive website design and development services that are optimized for search engines.',
                'category' => 'services',
                'status' => 'active',
                'views' => 67,
                'order' => 3,
                'created_at' => '2024-01-03'
            ],
            [
                'id' => 4,
                'question' => 'What are your service prices?',
                'answer' => 'Prices vary depending on the type of service and project requirements. You can request a custom quote through our contact form.',
                'category' => 'pricing',
                'status' => 'active',
                'views' => 234,
                'order' => 4,
                'created_at' => '2024-01-04'
            ],
            [
                'id' => 5,
                'question' => 'Do you work with small businesses?',
                'answer' => 'Absolutely! We work with businesses of all sizes from startups to large enterprises, and we provide customized solutions for each client.',
                'category' => 'general',
                'status' => 'active',
                'views' => 123,
                'order' => 5,
                'created_at' => '2024-01-05'
            ]
        ];
    }

    private function getContactSubmissions()
    {
        return [
            [
                'id' => 1,
                'name' => 'Ahmed Ali',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'subject' => 'Inquiry about marketing services',
                'message' => 'Hello, I would like to inquire about the digital marketing services you provide for my company. We are a startup in the e-commerce field and need a comprehensive marketing plan that includes social media management and paid advertising.',
                'company' => 'Smart Commerce Company',
                'service_interest' => 'Comprehensive Digital Marketing',
                'status' => 'new',
                'created_at' => '2024-01-15 10:30:00'
            ],
            [
                'id' => 2,
                'name' => 'Fatima Mohammed',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'subject' => 'Quote request for social media management',
                'message' => 'Hello, I own a restaurant and need social media management services. Please send a detailed quote that includes content creation, design, and daily posting.',
                'company' => 'Asala Restaurant',
                'service_interest' => 'Social Media Management',
                'status' => 'replied',
                'created_at' => '2024-01-14 14:20:00'
            ],
            [
                'id' => 3,
                'name' => 'Khalid Al-Saeed',
                'email' => '<EMAIL>',
                'phone' => '+966502345678',
                'subject' => 'Marketing consultation for tech company',
                'message' => 'We are a tech company specialized in app development and want to develop a marketing strategy to reach new clients. Can you provide a free consultation to discuss our needs?',
                'company' => 'Advanced Tech Solutions',
                'service_interest' => 'Marketing Consultation',
                'status' => 'read',
                'created_at' => '2024-01-13 16:45:00'
            ],
            [
                'id' => 4,
                'name' => 'Nora Ahmed',
                'email' => '<EMAIL>',
                'phone' => null,
                'subject' => 'Beauty salon marketing',
                'message' => 'I own a women\'s beauty salon and want to increase the number of clients through digital marketing. What services are suitable for my type of business?',
                'company' => 'Elegant Beauty Salon',
                'service_interest' => 'Women\'s Business Marketing',
                'status' => 'new',
                'created_at' => '2024-01-12 11:20:00'
            ],
            [
                'id' => 5,
                'name' => 'Mohammed Al-Otaibi',
                'email' => '<EMAIL>',
                'phone' => '+966505678901',
                'subject' => 'Construction company marketing',
                'message' => 'A newly established construction company that needs to build a strong brand identity and marketing plan to get new projects. We want to focus on LinkedIn and specialized websites.',
                'company' => 'Excellence Construction Company',
                'service_interest' => 'Brand Identity Building and B2B Marketing',
                'status' => 'read',
                'created_at' => '2024-01-11 09:30:00'
            ]
        ];
    }

    private function getQuoteRequests()
    {
        return [
            [
                'id' => 1,
                'name' => 'Al-Noor Trading Company',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'company' => 'Al-Noor Trading Company',
                'service' => 'Social Media Management',
                'budget' => '5000-10000 SAR',
                'project_description' => 'We need comprehensive management of our trading company\'s social media accounts. We want to increase customer engagement and build a strong presence on digital platforms.',
                'timeline' => '1 month',
                'additional_notes' => 'We prefer to focus primarily on Instagram and Twitter',
                'status' => 'pending',
                'created_at' => '2024-01-15 09:15:00'
            ],
            [
                'id' => 2,
                'name' => 'Riyadh Tech Foundation',
                'email' => '<EMAIL>',
                'phone' => '+************',
                'company' => 'Riyadh Tech Foundation',
                'service' => 'Comprehensive Advertising Campaigns',
                'budget' => '10000+ SAR',
                'project_description' => 'We want to launch a comprehensive advertising campaign for our tech services targeting companies and institutions. We need an integrated strategy including Google Ads and LinkedIn.',
                'timeline' => '2-3 months',
                'additional_notes' => 'We have a flexible budget for successful campaigns',
                'status' => 'sent',
                'created_at' => '2024-01-13 16:45:00'
            ],
            [
                'id' => 3,
                'name' => 'Elegant Beauty Salon',
                'email' => '<EMAIL>',
                'phone' => '+966502345678',
                'company' => 'Elegant Beauty Salon',
                'service' => 'Women\'s Business Marketing',
                'budget' => '3000-5000 SAR',
                'project_description' => 'A women\'s beauty salon that needs digital marketing targeting women in the area. We want to increase the number of clients and build a strong reputation.',
                'timeline' => '6 weeks',
                'additional_notes' => 'We prefer to focus on Instagram and Snapchat',
                'status' => 'accepted',
                'created_at' => '2024-01-12 11:20:00'
            ],
            [
                'id' => 4,
                'name' => 'مطعم الأصالة',
                'email' => '<EMAIL>',
                'phone' => '+966505678901',
                'company' => 'مطعم الأصالة',
                'service' => 'تسويق المطاعم والأغذية',
                'budget' => '2000-4000 ر.س',
                'project_description' => 'مطعم يقدم الأكلات الشعبية ويحتاج إلى تسويق رقمي لزيادة الطلبات وجذب عملاء جدد. نريد التركيز على التوصيل والطلبات الإلكترونية.',
                'timeline' => '4-6 أسابيع',
                'additional_notes' => 'نحتاج إلى تصوير احترافي للأطباق',
                'status' => 'rejected',
                'created_at' => '2024-01-10 14:30:00'
            ]
        ];
    }
}
