@extends('admin.layouts.app')

@section('title', 'إضافة سؤال جديد')
@section('page-title', 'إضافة سؤال جديد')

@section('content')
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ route('admin.faq.index') }}">إدارة الأسئلة الشائعة</a></li>
                <li class="breadcrumb-item active">إضافة سؤال جديد</li>
            </ol>
        </nav>
    </div>
    <a href="{{ route('admin.faq.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        العودة للقائمة
    </a>
</div>

<form method="POST" action="{{ route('admin.faq.store') }}">
    @csrf
    
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Question and Answer -->
            <div class="card mb-4" data-aos="fade-up">
                <div class="card-header">
                    <h5><i class="fas fa-question-circle me-2"></i>السؤال والإجابة</h5>
                </div>
                <div class="card-body">
                    <!-- Arabic Question -->
                    <div class="mb-3">
                        <label for="question_ar" class="form-label">
                            <i class="fas fa-question me-1"></i>
                            السؤال (عربي) <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control @error('question_ar') is-invalid @enderror" 
                                  id="question_ar" 
                                  name="question_ar" 
                                  rows="3" 
                                  required
                                  placeholder="أدخل السؤال باللغة العربية">{{ old('question_ar') }}</textarea>
                        @error('question_ar')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- English Question -->
                    <div class="mb-3">
                        <label for="question_en" class="form-label">
                            <i class="fas fa-question me-1"></i>
                            السؤال (إنجليزي) <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control @error('question_en') is-invalid @enderror" 
                                  id="question_en" 
                                  name="question_en" 
                                  rows="3" 
                                  required
                                  placeholder="Enter question in English">{{ old('question_en') }}</textarea>
                        @error('question_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Arabic Answer -->
                    <div class="mb-3">
                        <label for="answer_ar" class="form-label">
                            <i class="fas fa-comment me-1"></i>
                            الإجابة (عربي) <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control @error('answer_ar') is-invalid @enderror" 
                                  id="answer_ar" 
                                  name="answer_ar" 
                                  rows="6" 
                                  required
                                  placeholder="أدخل الإجابة باللغة العربية">{{ old('answer_ar') }}</textarea>
                        @error('answer_ar')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- English Answer -->
                    <div class="mb-3">
                        <label for="answer_en" class="form-label">
                            <i class="fas fa-comment me-1"></i>
                            الإجابة (إنجليزي) <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control @error('answer_en') is-invalid @enderror" 
                                  id="answer_en" 
                                  name="answer_en" 
                                  rows="6" 
                                  required
                                  placeholder="Enter answer in English">{{ old('answer_en') }}</textarea>
                        @error('answer_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Additional Information -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle me-2"></i>معلومات إضافية</h5>
                </div>
                <div class="card-body">
                    <!-- Keywords -->
                    <div class="mb-3">
                        <label for="keywords" class="form-label">
                            <i class="fas fa-tags me-1"></i>
                            الكلمات المفتاحية
                        </label>
                        <input type="text" 
                               class="form-control" 
                               id="keywords" 
                               name="keywords" 
                               value="{{ old('keywords') }}" 
                               placeholder="كلمات مفتاحية مفصولة بفاصلة">
                        <small class="text-muted">ستساعد في البحث والتصنيف</small>
                    </div>
                    
                    <!-- Related Links -->
                    <div class="mb-3">
                        <label for="related_links" class="form-label">
                            <i class="fas fa-link me-1"></i>
                            روابط ذات صلة
                        </label>
                        <textarea class="form-control" 
                                  id="related_links" 
                                  name="related_links" 
                                  rows="3" 
                                  placeholder="روابط مفيدة ذات صلة بالسؤال (رابط واحد في كل سطر)">{{ old('related_links') }}</textarea>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Publish Settings -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="card-header">
                    <h5><i class="fas fa-cog me-2"></i>إعدادات النشر</h5>
                </div>
                <div class="card-body">
                    <!-- Status -->
                    <div class="mb-3">
                        <label for="status" class="form-label">
                            <i class="fas fa-toggle-on me-1"></i>
                            حالة السؤال
                        </label>
                        <select class="form-select" id="status" name="status">
                            <option value="active" {{ old('status', 'active') === 'active' ? 'selected' : '' }}>نشط</option>
                            <option value="inactive" {{ old('status') === 'inactive' ? 'selected' : '' }}>غير نشط</option>
                        </select>
                        <small class="text-muted">الأسئلة النشطة فقط ستظهر للزوار</small>
                    </div>
                    
                    <!-- Featured -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="featured" 
                                   name="featured" 
                                   value="1"
                                   {{ old('featured') ? 'checked' : '' }}>
                            <label class="form-check-label" for="featured">
                                <i class="fas fa-star me-1"></i>
                                سؤال مميز
                            </label>
                        </div>
                        <small class="text-muted">سيظهر في أعلى قائمة الأسئلة</small>
                    </div>
                </div>
            </div>
            
            <!-- Category and Priority -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="card-header">
                    <h5><i class="fas fa-folder me-2"></i>التصنيف والأولوية</h5>
                </div>
                <div class="card-body">
                    <!-- Category -->
                    <div class="mb-3">
                        <label for="category" class="form-label">
                            <i class="fas fa-list me-1"></i>
                            الفئة <span class="text-danger">*</span>
                        </label>
                        <select class="form-select @error('category') is-invalid @enderror" 
                                id="category" 
                                name="category" 
                                required>
                            <option value="">اختر الفئة</option>
                            <option value="general" {{ old('category') === 'general' ? 'selected' : '' }}>عام</option>
                            <option value="services" {{ old('category') === 'services' ? 'selected' : '' }}>الخدمات</option>
                            <option value="pricing" {{ old('category') === 'pricing' ? 'selected' : '' }}>الأسعار</option>
                            <option value="technical" {{ old('category') === 'technical' ? 'selected' : '' }}>تقني</option>
                            <option value="support" {{ old('category') === 'support' ? 'selected' : '' }}>الدعم</option>
                        </select>
                        @error('category')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Order/Priority -->
                    <div class="mb-3">
                        <label for="order" class="form-label">
                            <i class="fas fa-sort-numeric-down me-1"></i>
                            ترتيب العرض
                        </label>
                        <input type="number" 
                               class="form-control" 
                               id="order" 
                               name="order" 
                               value="{{ old('order', 1) }}" 
                               min="1" 
                               max="999">
                        <small class="text-muted">الرقم الأصغر يظهر أولاً</small>
                    </div>
                </div>
            </div>
            
            <!-- Help and Tips -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="400">
                <div class="card-header">
                    <h5><i class="fas fa-lightbulb me-2"></i>نصائح للكتابة</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>نصائح مهمة:</h6>
                        <ul class="mb-0 small">
                            <li>اكتب السؤال بوضوح ومباشرة</li>
                            <li>قدم إجابة شاملة ومفيدة</li>
                            <li>استخدم لغة بسيطة ومفهومة</li>
                            <li>أضف أمثلة عملية عند الحاجة</li>
                            <li>تأكد من صحة المعلومات</li>
                        </ul>
                    </div>
                    
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>تجنب:</h6>
                        <ul class="mb-0 small">
                            <li>الإجابات المعقدة أو الطويلة جداً</li>
                            <li>استخدام مصطلحات تقنية معقدة</li>
                            <li>ترك الإجابة غير مكتملة</li>
                            <li>نسخ المحتوى من مصادر أخرى</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Preview -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="500">
                <div class="card-header">
                    <h5><i class="fas fa-eye me-2"></i>معاينة</h5>
                </div>
                <div class="card-body">
                    <button type="button" class="btn btn-outline-info w-100" onclick="previewFAQ()">
                        <i class="fas fa-eye me-2"></i>
                        معاينة السؤال
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="row">
        <div class="col-12">
            <div class="card" data-aos="fade-up" data-aos-delay="600">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" name="action" value="save" class="btn btn-primary me-2">
                                <i class="fas fa-save me-2"></i>
                                حفظ السؤال
                            </button>
                            <button type="submit" name="action" value="save_and_continue" class="btn btn-success me-2">
                                <i class="fas fa-plus me-2"></i>
                                حفظ وإضافة آخر
                            </button>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="saveDraft()">
                                <i class="fas fa-file-alt me-2"></i>
                                حفظ كمسودة
                            </button>
                            <a href="{{ route('admin.faq.index') }}" class="btn btn-outline-danger">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">معاينة السؤال</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <!-- Preview content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="document.querySelector('form').submit()">
                    <i class="fas fa-save me-2"></i>
                    حفظ السؤال
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Preview FAQ function
function previewFAQ() {
    const questionAr = document.getElementById('question_ar').value;
    const questionEn = document.getElementById('question_en').value;
    const answerAr = document.getElementById('answer_ar').value;
    const answerEn = document.getElementById('answer_en').value;
    const category = document.getElementById('category').value;
    const status = document.getElementById('status').value;
    const featured = document.getElementById('featured').checked;
    
    if (!questionAr || !answerAr) {
        alert('يرجى إدخال السؤال والإجابة باللغة العربية على الأقل');
        return;
    }
    
    const categoryNames = {
        'general': 'عام',
        'services': 'الخدمات',
        'pricing': 'الأسعار',
        'technical': 'تقني',
        'support': 'الدعم'
    };
    
    const previewContent = `
        <div class="faq-preview">
            ${featured ? '<div class="alert alert-warning"><i class="fas fa-star me-2"></i>سؤال مميز</div>' : ''}
            
            <div class="mb-4">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <span class="badge bg-primary">${categoryNames[category] || 'غير محدد'}</span>
                    <span class="badge bg-${status === 'active' ? 'success' : 'secondary'}">${status === 'active' ? 'نشط' : 'غير نشط'}</span>
                </div>
            </div>
            
            <div class="accordion" id="previewAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#arabicPreview">
                            <i class="fas fa-question-circle me-2"></i>
                            النسخة العربية
                        </button>
                    </h2>
                    <div id="arabicPreview" class="accordion-collapse collapse show">
                        <div class="accordion-body">
                            <div class="mb-3">
                                <h6 class="text-primary">السؤال:</h6>
                                <p class="fw-bold">${questionAr}</p>
                            </div>
                            <div>
                                <h6 class="text-primary">الإجابة:</h6>
                                <div class="border-start border-3 border-primary ps-3">
                                    ${answerAr.replace(/\n/g, '<br>')}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                ${questionEn && answerEn ? `
                <div class="accordion-item">
                    <h2 class="accordion-header">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#englishPreview">
                            <i class="fas fa-question-circle me-2"></i>
                            English Version
                        </button>
                    </h2>
                    <div id="englishPreview" class="accordion-collapse collapse">
                        <div class="accordion-body">
                            <div class="mb-3">
                                <h6 class="text-primary">Question:</h6>
                                <p class="fw-bold">${questionEn}</p>
                            </div>
                            <div>
                                <h6 class="text-primary">Answer:</h6>
                                <div class="border-start border-3 border-primary ps-3">
                                    ${answerEn.replace(/\n/g, '<br>')}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                ` : ''}
            </div>
        </div>
    `;
    
    document.getElementById('previewContent').innerHTML = previewContent;
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    modal.show();
}

// Save as draft function
function saveDraft() {
    document.getElementById('status').value = 'inactive';
    document.querySelector('form').submit();
}

// Auto-save functionality
let autoSaveInterval;
function startAutoSave() {
    autoSaveInterval = setInterval(function() {
        const formData = new FormData(document.querySelector('form'));
        localStorage.setItem('faq_draft', JSON.stringify(Object.fromEntries(formData)));
        showAutoSaveIndicator();
    }, 120000); // 2 minutes
}

function showAutoSaveIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'alert alert-info alert-dismissible fade show position-fixed';
    indicator.style.top = '20px';
    indicator.style.right = '20px';
    indicator.style.zIndex = '9999';
    indicator.innerHTML = `
        <i class="fas fa-save me-2"></i>
        تم حفظ المسودة تلقائياً
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(indicator);
    
    setTimeout(function() {
        if (indicator.parentNode) {
            indicator.remove();
        }
    }, 3000);
}

// Load draft from localStorage if exists
function loadDraft() {
    const draft = localStorage.getItem('faq_draft');
    if (draft && confirm('تم العثور على مسودة محفوظة. هل تريد استعادتها؟')) {
        const draftData = JSON.parse(draft);
        Object.keys(draftData).forEach(key => {
            const element = document.querySelector(`[name="${key}"]`);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = draftData[key] === '1';
                } else {
                    element.value = draftData[key];
                }
            }
        });
    }
}

// Character counters
function setupCharacterCounters() {
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        const counter = document.createElement('small');
        counter.className = 'text-muted';
        counter.style.float = 'left';
        textarea.parentNode.appendChild(counter);
        
        function updateCounter() {
            const length = textarea.value.length;
            counter.textContent = `${length} حرف`;
            
            if (length > 500) {
                counter.style.color = 'red';
            } else {
                counter.style.color = 'inherit';
            }
        }
        
        textarea.addEventListener('input', updateCounter);
        updateCounter();
    });
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    startAutoSave();
    loadDraft();
    setupCharacterCounters();
    
    // Clear draft when form is successfully submitted
    document.querySelector('form').addEventListener('submit', function() {
        localStorage.removeItem('faq_draft');
        clearInterval(autoSaveInterval);
    });
});

// Warn user about unsaved changes
window.addEventListener('beforeunload', function(e) {
    const form = document.querySelector('form');
    const formData = new FormData(form);
    let hasContent = false;
    
    for (let [key, value] of formData.entries()) {
        if (value && value.toString().trim() !== '') {
            hasContent = true;
            break;
        }
    }
    
    if (hasContent) {
        e.preventDefault();
        e.returnValue = '';
    }
});
</script>
@endpush
