<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- SEO Meta Tags -->
    <title>@yield('title', 'Agent Marketing - Professional Digital Marketing Agency | SEO, Social Media, PPC')</title>
    <meta name="description" content="@yield('description', 'Professional digital marketing agency specializing in SEO, social media management, PPC campaigns, and brand identity design. Grow your business with proven marketing strategies.')">
    <meta name="keywords" content="@yield('keywords', 'digital marketing, SEO, social media marketing, PPC, Google Ads, Facebook Ads, brand identity, marketing agency, online marketing, digital advertising')">
    <meta name="author" content="Agent Marketing">
    <meta name="robots" content="index, follow">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@yield('og_title', 'Agent Marketing - Professional Digital Marketing Agency')">
    <meta property="og:description" content="@yield('og_description', 'Transform your business with our professional digital marketing services. Expert SEO, social media management, and advertising campaigns.')">

    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url()->current() }}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #f59e0b;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --bg-light: #f8fafc;
            --gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: {{ session('locale', 'ar') === 'ar' ? "'Cairo', sans-serif" : "'Inter', sans-serif" }};
            line-height: 1.6;
            color: var(--text-dark);
        }
        
        .navbar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            padding: 1rem 0;
            min-height: 70px;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: var(--primary-color) !important;
            text-decoration: none;
            display: flex;
            align-items: center;
            white-space: nowrap;
        }
        
        .navbar-brand:hover {
            color: var(--primary-color) !important;
            text-decoration: none;
        }
        
        .navbar-nav {
            align-items: center;
        }
        
        .nav-link {
            font-weight: 500;
            color: var(--text-dark) !important;
            transition: color 0.3s ease;
            margin: 0 0.25rem;
            padding: 0.5rem 0.75rem !important;
            border-radius: 8px;
            white-space: nowrap;
        }
        
        .nav-link:hover {
            color: var(--primary-color) !important;
            background-color: rgba(37, 99, 235, 0.1);
        }
        
        .navbar-toggler {
            border: none;
            padding: 0.25rem 0.5rem;
            font-size: 1.1rem;
        }
        
        .navbar-toggler:focus {
            box-shadow: none;
        }
        
        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }
        

        
        .btn-primary {
            background: var(--gradient);
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
        }
        
        .btn-outline-primary {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            padding: 0.75rem 2rem;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-outline-primary:hover {
            background: var(--primary-color);
            transform: translateY(-2px);
        }
        
        .hero-section {
            background: var(--gradient);
            color: white;
            padding: 120px 0;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="rgba(255,255,255,0.1)" points="0,1000 1000,0 1000,1000"/></svg>');
            background-size: cover;
        }
        
        .section-padding {
            padding: 80px 0;
        }
        
        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            position: relative;
        }
        
        .section-subtitle {
            font-size: 1.1rem;
            color: var(--text-light);
            margin-bottom: 3rem;
        }
        
        .card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .service-icon {
            width: 80px;
            height: 80px;
            background: var(--gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: white;
            margin: 0 auto 1.5rem;
        }
        
        .stats-section {
            background: var(--bg-light);
        }
        
        .stat-item {
            text-align: center;
            padding: 2rem;
        }
        
        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            color: var(--primary-color);
            display: block;
        }
        
        .stat-label {
            font-size: 1.1rem;
            color: var(--text-light);
            margin-top: 0.5rem;
        }
        
        .testimonial-card {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin: 1rem;
            position: relative;
        }
        
        .testimonial-card::before {
            content: '"';
            position: absolute;
            top: -10px;
            left: 20px;
            font-size: 4rem;
            color: var(--primary-color);
            opacity: 0.3;
        }
        
        .rating {
            color: #fbbf24;
            margin-bottom: 1rem;
        }
        
        .footer {
            background: #1f2937;
            color: white;
            padding: 60px 0 20px;
        }
        
        .footer h5 {
            color: var(--accent-color);
            margin-bottom: 1.5rem;
        }
        
        .footer a {
            color: #d1d5db;
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .footer a:hover {
            color: var(--accent-color);
        }
        
        .social-links a {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            color: white;
            text-align: center;
            line-height: 40px;
            border-radius: 50%;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        
        .social-links a:hover {
            background: var(--accent-color);
            transform: translateY(-3px);
        }
        
        .lang-switcher {
            margin-left: 1rem;
        }
        
        .lang-switcher a {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            text-decoration: none;
            margin: 0 0.25rem;
            transition: all 0.3s ease;
        }
        
        .lang-switcher a.active {
            background: var(--primary-color);
            color: white;
        }
        
        /* RTL Specific Styles */
        [dir="rtl"] .navbar-nav {
            margin-right: auto;
            margin-left: 0;
        }
        
        [dir="rtl"] .lang-switcher {
            margin-right: 1rem;
            margin-left: 0;
        }
        
        /* Responsive Design */
        
        /* Large screens (1200px and up) */
        @media (min-width: 1200px) {
            .container {
                max-width: 1140px;
            }
        }
        
        /* Medium screens (992px to 1199px) */
        @media (max-width: 1199px) {
            .section-title {
                font-size: 2.2rem;
            }
            
            .hero-section h1 {
                font-size: 2.5rem;
            }
        }
        
        /* Small screens (768px to 991px) */
        @media (max-width: 991px) {
            .navbar-nav {
                text-align: center;
                margin-top: 1rem;
            }
            
            .navbar-nav .nav-link {
                padding: 0.75rem 1rem;
                margin: 0.25rem 0;
            }
            
            .hero-section {
                padding: 60px 0;
                text-align: center;
            }
            
            .hero-section h1 {
                font-size: 2.2rem;
                margin-bottom: 1rem;
            }
            
            .hero-section p {
                font-size: 1.1rem;
                margin-bottom: 2rem;
            }
            
            .section-padding {
                padding: 60px 0;
            }
            
            .section-title {
                font-size: 2rem;
                margin-bottom: 1.5rem;
            }
            
            .card {
                margin-bottom: 2rem;
            }
            
            .stats-section .col-md-3 {
                margin-bottom: 2rem;
            }
        }
        
        /* Navigation Mobile Responsive Styles */
        @media (max-width: 991px) {
            .navbar {
                padding: 0.75rem 0;
            }
            
            .navbar-brand {
                font-size: 1.25rem;
            }
            
            .navbar-collapse {
                margin-top: 1rem;
                padding-top: 1rem;
                border-top: 1px solid rgba(0, 0, 0, 0.1);
            }
            
            .navbar-nav {
                align-items: stretch;
                margin-bottom: 1rem;
            }
            
            .nav-link {
                padding: 0.75rem 0 !important;
                margin: 0;
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
                border-radius: 0;
            }
            
            .nav-link:hover {
                background-color: rgba(37, 99, 235, 0.05);
            }
            
            .d-flex.align-items-center {
                justify-content: center;
                margin-top: 1rem;
            }
            
            .btn-primary.btn-sm {
                width: 100%;
                max-width: 200px;
            }
        }
        
        @media (max-width: 767px) {
            .navbar {
                padding: 0.5rem 0;
            }
            
            .navbar-brand {
                font-size: 1.1rem;
            }
            
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }
        
        /* Desktop improvements */
        @media (min-width: 992px) {
            .navbar-nav .nav-item:last-child {
                margin-right: 1rem;
            }
        }
        
        /* Mobile screens (576px to 767px) */
        @media (max-width: 767px) {
            .navbar-brand {
                font-size: 1.3rem;
            }
            
            .navbar-toggler {
                border: none;
                padding: 0.25rem 0.5rem;
            }
            
            .hero-section {
                padding: 40px 0;
            }
            
            .hero-section h1 {
                font-size: 1.8rem;
                line-height: 1.3;
            }
            
            .hero-section p {
                font-size: 1rem;
            }
            
            .btn {
                padding: 0.6rem 1.5rem;
                font-size: 0.9rem;
                margin: 0.25rem;
                display: inline-block;
                width: auto;
            }
            
            .section-padding {
                padding: 40px 0;
            }
            
            .section-title {
                font-size: 1.7rem;
                margin-bottom: 1rem;
            }
            
            .stat-number {
                font-size: 2rem;
            }
            
            .stat-label {
                font-size: 1rem;
            }
            
            .card {
                margin-bottom: 1.5rem;
            }
            
            .card-body {
                padding: 1.5rem;
            }
            
            .testimonial-card {
                padding: 1.5rem;
                margin-bottom: 1.5rem;
            }
            
            .footer {
                text-align: center;
            }
            
            .footer .col-md-6 {
                margin-bottom: 1rem;
            }
            
            .social-links {
                justify-content: center;
                margin-top: 1rem;
            }
        }
        
        /* Extra small screens (up to 575px) */
        @media (max-width: 575px) {
            .container {
                padding-left: 15px;
                padding-right: 15px;
            }
            
            .hero-section h1 {
                font-size: 1.6rem;
            }
            
            .section-title {
                font-size: 1.5rem;
            }
            
            .btn {
                width: 100%;
                margin-bottom: 0.5rem;
            }
            
            .btn:last-child {
                margin-bottom: 0;
            }
            
            .stat-number {
                font-size: 1.8rem;
            }
            
            .card-body {
                padding: 1rem;
            }
            
            .navbar-nav .nav-link {
                font-size: 1.1rem;
                padding: 1rem;
            }
            
            .lang-switcher {
                margin-top: 1rem;
                text-align: center;
            }
            
            .footer ul {
                text-align: center;
                margin-bottom: 1.5rem;
            }
        }
        
        /* Landscape orientation for mobile */
        @media (max-width: 767px) and (orientation: landscape) {
            .hero-section {
                padding: 30px 0;
            }
            
            .section-padding {
                padding: 30px 0;
            }
        }
        
        /* High DPI screens */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .navbar {
                backdrop-filter: blur(15px);
            }
        }
    </style>
    
    @stack('styles')
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand" href="{{ route('home') }}">
                <i class="fas fa-rocket me-2"></i>
                Agent Marketing
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('home') }}">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('about') }}">About Us</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('services') }}">Services</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('portfolio') }}">Portfolio</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('pricing') }}">Pricing</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('testimonials') }}">Testimonials</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('blog') }}">Blog</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ route('contact') }}">Contact</a>
                    </li>
                </ul>
                
                <div class="d-flex align-items-center">
                    <a href="{{ route('quote') }}" class="btn btn-primary btn-sm">
                        Get Quote
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main>
        @yield('content')
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5>
                        <i class="fas fa-rocket me-2"></i>
                        Agent Marketing
                    </h5>
                    <p class="mb-4">
                        Professional digital marketing agency helping you achieve your business goals through innovative and strategic marketing solutions.
                    </p>
                    <div class="social-links">
                        <a href="#"><i class="fab fa-facebook-f"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                        <a href="#"><i class="fab fa-linkedin-in"></i></a>
                        <a href="#"><i class="fab fa-youtube"></i></a>
                    </div>
                </div>
                
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5>Quick Links</h5>
                    <ul class="list-unstyled">
                        <li><a href="{{ route('home') }}">Home</a></li>
                        <li><a href="{{ route('about') }}">About Us</a></li>
                        <li><a href="{{ route('services') }}">Services</a></li>
                        <li><a href="{{ route('portfolio') }}">Portfolio</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5>Our Services</h5>
                    <ul class="list-unstyled">
                        <li><a href="#">Ad Campaign Management</a></li>
                        <li><a href="#">Social Media Management</a></li>
                        <li><a href="#">SEO Optimization</a></li>
                        <li><a href="#">Brand Identity Design</a></li>
                    </ul>
                </div>
                
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5>Contact Info</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-phone me-2"></i> +966 50 123 4567</li>
                        <li><i class="fas fa-envelope me-2"></i> <EMAIL></li>
                        <li><i class="fas fa-map-marker-alt me-2"></i> Riyadh, Saudi Arabia</li>
                        <li><i class="fas fa-clock me-2"></i> Sun - Thu: 9:00 AM - 6:00 PM</li>
                    </ul>
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="mb-0">
                        &copy; {{ date('Y') }} Agent Marketing. All rights reserved.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="me-3">{{ session('locale', 'ar') === 'ar' ? 'سياسة الخصوصية' : 'Privacy Policy' }}</a>
                    <a href="#">{{ session('locale', 'ar') === 'ar' ? 'شروط الاستخدام' : 'Terms of Service' }}</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- AOS Animation -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });
        
        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255, 255, 255, 0.98)';
            } else {
                navbar.style.background = 'rgba(255, 255, 255, 0.95)';
            }
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
    
    @stack('scripts')
</body>
</html>
