<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class PricingController extends Controller
{
    public function index()
    {
        $packages = [
            [
                'id' => 'starter',
                'name' => 'Starter Package',
                'price' => '2,500',
                'duration' => 'Monthly',
                'description' => 'Perfect for startups and small businesses',
                'features' => [
                    'Management of 1 social media account',
                    '10 posts per month',
                    'Visual content design',
                    'Comments and messages response',
                    'Detailed monthly report',
                    'Basic technical support'
                ],
                'popular' => false,
                'color' => 'primary',
                'icon' => 'fas fa-rocket'
            ],
            [
                'id' => 'professional',
                'name' => 'Professional Package',
                'price' => '4,500',
                'duration' => 'Monthly',
                'description' => 'Best for medium and growing businesses',
                'features' => [
                    'Management of 3 social media accounts',
                    '20 posts per month',
                    'Advanced visual content design',
                    '1 advertising campaign per month',
                    'Competitor analysis',
                    'Content strategy',
                    'Detailed weekly reports',
                    'Advanced technical support'
                ],
                'popular' => true,
                'color' => 'success',
                'icon' => 'fas fa-star'
            ],
            [
                'id' => 'enterprise',
                'name' => 'Enterprise Package',
                'price' => '8,500',
                'duration' => 'Monthly',
                'description' => 'For large companies and enterprises',
                'features' => [
                    'Unlimited social media accounts',
                    '40 posts per month',
                    'Multiple advertising campaigns',
                    'Comprehensive marketing strategy',
                    'Dedicated account manager',
                    'Daily reports and advanced analytics',
                    '24/7 technical support'
                ],
                'popular' => false,
                'color' => 'warning',
                'icon' => 'fas fa-crown'
            ]
        ];

        $addons = [
            [
                'name' => 'Brand Identity Design',
                'price' => '3,500',
                'description' => 'Logo design and complete visual identity',
                'icon' => 'fas fa-palette'
            ],
            [
                'name' => 'Website Development',
                'price' => '5,500',
                'description' => 'Responsive website optimized for search engines',
                'icon' => 'fas fa-globe'
            ],
            [
                'name' => 'Video Production',
                'price' => '2,800',
                'description' => 'Professional marketing video production',
                'icon' => 'fas fa-video'
            ],
            [
                'name' => 'Photography',
                'price' => '1,200',
                'description' => 'Professional photography session for products or services',
                'icon' => 'fas fa-camera'
            ]
        ];

        $faqs = [
            [
                'question' => 'Can I change my package anytime?',
                'answer' => 'Yes, you can upgrade or downgrade your package anytime with 7 days notice'
            ],
            [
                'question' => 'Do prices include VAT?',
                'answer' => 'Displayed prices do not include 15% VAT'
            ],
            [
                'question' => 'What is the contract duration?',
                'answer' => 'Minimum contract is 3 months, with monthly renewal option afterwards'
            ],
            [
                'question' => 'Do you provide service guarantee?',
                'answer' => 'Yes, we guarantee achieving agreed results or 50% package value refund'
            ]
        ];

        return view('pricing.index', compact('packages', 'addons', 'faqs'));
    }
}
