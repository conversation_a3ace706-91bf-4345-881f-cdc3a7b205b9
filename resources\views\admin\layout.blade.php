<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Agent Marketing Admin Dashboard">
    <meta name="author" content="Agent Marketing">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Dashboard') - Agent Marketing</title>

    <!-- Custom fonts for this template-->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet" type="text/css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom styles for this template-->
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fc;
        }
        
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #4e73df 10%, #224abe 100%);
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        
        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 1rem;
            border-radius: 0.35rem;
            margin: 0.25rem 1rem;
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: #fff;
            background-color: rgba(255, 255, 255, 0.1);
        }
        
        .sidebar .nav-link i {
            margin-right: 0.5rem;
            width: 1.5rem;
            text-align: center;
        }
        
        .sidebar-brand {
            height: 4.375rem;
            text-decoration: none;
            font-size: 1rem;
            font-weight: 800;
            padding: 1.5rem 1rem;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 0.05rem;
            z-index: 1;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .topbar {
            height: 4.375rem;
            background-color: #fff;
            border-bottom: 1px solid #e3e6f0;
        }
        
        .card {
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            border: none;
        }
        
        .card-header {
            background-color: #f8f9fc;
            border-bottom: 1px solid #e3e6f0;
        }
        
        .btn-primary {
            background-color: #4e73df;
            border-color: #4e73df;
        }
        
        .btn-primary:hover {
            background-color: #2e59d9;
            border-color: #2653d4;
        }
        
        .text-primary {
            color: #4e73df !important;
        }
        
        .bg-primary {
            background-color: #4e73df !important;
        }
        
        .border-left-primary {
            border-left: 0.25rem solid #4e73df !important;
        }
        
        .main-content {
            padding: 2rem;
        }
        
        .stats-card {
            border-left: 0.25rem solid;
            transition: transform 0.2s;
        }
        
        .stats-card:hover {
            transform: translateY(-2px);
        }
        
        .table th {
            border-top: none;
            font-weight: 600;
            color: #5a5c69;
        }
        
        .badge {
            font-size: 0.75em;
        }
        
        .dropdown-menu {
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        }
        
        .alert {
            border: none;
            border-radius: 0.35rem;
        }
        
        /* Responsive Design for Admin Panel */
        
        /* Large screens (1200px and up) */
        @media (min-width: 1200px) {
            .main-content {
                padding: 2.5rem;
            }
            
            .card {
                margin-bottom: 2rem;
            }
        }
        
        /* Medium screens (992px to 1199px) */
        @media (max-width: 1199px) {
            .main-content {
                padding: 2rem;
            }
            
            .sidebar {
                width: 220px;
            }
            
            .main-content {
                margin-right: 220px;
            }
        }
        
        /* Small screens (768px to 991px) */
        @media (max-width: 991px) {
            .sidebar {
                position: fixed;
                top: 0;
                right: -280px;
                width: 280px;
                height: 100vh;
                z-index: 1050;
                transition: right 0.3s ease;
                overflow-y: auto;
            }
            
            .sidebar.show {
                right: 0;
                box-shadow: 0 0 50px rgba(0, 0, 0, 0.3);
            }
            
            .main-content {
                margin-right: 0;
                padding: 1.5rem;
            }
            
            .topbar {
                padding: 0 1rem;
            }
            
            .card {
                margin-bottom: 1.5rem;
            }
            
            .table-responsive {
                font-size: 0.9rem;
            }
        }
        
        /* Mobile screens (576px to 767px) */
        @media (max-width: 767px) {
            .sidebar {
                position: fixed;
                top: 0;
                right: -100%;
                width: 100%;
                height: 100vh;
                z-index: 1050;
                transition: right 0.3s ease;
                overflow-y: auto;
            }
            
            .sidebar.show {
                right: 0;
            }
            
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }
            
            .topbar {
                padding: 0 0.5rem;
                height: auto;
                min-height: 4.375rem;
            }
            
            .topbar .navbar-nav {
                flex-direction: row;
                align-items: center;
            }
            
            .card {
                margin-bottom: 1rem;
            }
            
            .card-body {
                padding: 1rem;
            }
            
            .btn {
                padding: 0.5rem 1rem;
                font-size: 0.9rem;
                margin-bottom: 0.5rem;
            }
            
            .btn-sm {
                padding: 0.25rem 0.75rem;
                font-size: 0.8rem;
            }
            
            .table {
                font-size: 0.85rem;
            }
            
            .table th,
            .table td {
                padding: 0.5rem;
            }
            
            .form-control {
                font-size: 1rem;
            }
            
            .modal-dialog {
                margin: 0.5rem;
            }
            
            .stats-card {
                margin-bottom: 1rem;
            }
            
            .stats-card .card-body {
                padding: 1rem;
                text-align: center;
            }
            
            .dropdown-menu {
                position: absolute;
                right: 0;
                left: auto;
                min-width: 200px;
            }
        }
        
        /* Extra small screens (up to 575px) */
        @media (max-width: 575px) {
            .main-content {
                padding: 0.75rem;
            }
            
            .topbar {
                padding: 0 0.25rem;
            }
            
            .card-body {
                padding: 0.75rem;
            }
            
            .btn {
                width: 100%;
                margin-bottom: 0.5rem;
            }
            
            .btn-group .btn {
                width: auto;
            }
            
            .table-responsive {
                font-size: 0.8rem;
            }
            
            .table th,
            .table td {
                padding: 0.25rem;
                white-space: nowrap;
            }
            
            .form-group {
                margin-bottom: 1rem;
            }
            
            .form-control {
                font-size: 16px; /* Prevents zoom on iOS */
            }
            
            .modal-dialog {
                margin: 0.25rem;
            }
            
            .sidebar .nav-link {
                padding: 1rem;
                font-size: 1.1rem;
            }
            
            .sidebar .nav-link i {
                margin-left: 0.75rem;
                width: 2rem;
            }
            
            .sidebar-brand {
                padding: 2rem 1rem;
                font-size: 1.2rem;
            }
            
            .alert {
                padding: 0.75rem;
                font-size: 0.9rem;
            }
        }
        
        /* Landscape orientation for tablets */
        @media (max-width: 1024px) and (orientation: landscape) {
            .sidebar {
                width: 250px;
            }
            
            .main-content {
                padding: 1.5rem;
            }
        }
        
        /* Touch device optimizations */
        @media (hover: none) and (pointer: coarse) {
            .sidebar .nav-link {
                padding: 1.25rem;
                min-height: 48px;
            }
            
            .btn {
                min-height: 44px;
                padding: 0.75rem 1.5rem;
            }
            
            .form-control {
                min-height: 44px;
                padding: 0.75rem;
            }
        }
        
        /* Sidebar overlay for mobile */
        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1040;
        }
        
        @media (max-width: 991px) {
            .sidebar-overlay.show {
                display: block;
            }
        }
        
        /* Body scroll control */
        body.overflow-hidden {
            overflow: hidden;
            position: fixed;
            width: 100%;
        }
        
        /* Smooth transitions */
        .sidebar,
        .sidebar-overlay {
            transition: all 0.3s ease;
        }
        
        /* Focus styles for accessibility */
        .sidebar .nav-link:focus,
        .btn:focus {
            outline: 2px solid #4e73df;
            outline-offset: 2px;
        }
    </style>
    
    @stack('styles')
</head>

<body>
    <div class="d-flex">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <a class="sidebar-brand" href="{{ route('admin.dashboard') }}">
                <div class="sidebar-brand-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="sidebar-brand-text mx-2">Agent Marketing</div>
            </a>

            <hr class="sidebar-divider my-0" style="border-color: rgba(255,255,255,0.15);">

            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}" 
                       href="{{ route('admin.dashboard') }}">
                        <i class="fas fa-fw fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </li>

                <hr class="sidebar-divider" style="border-color: rgba(255,255,255,0.15);">

                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.blog.*') ? 'active' : '' }}" 
                       href="{{ route('admin.blog.index') }}">
                        <i class="fas fa-fw fa-blog"></i>
                        <span>Blog Management</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.faq.*') ? 'active' : '' }}" 
                       href="{{ route('admin.faq.index') }}">
                        <i class="fas fa-fw fa-question-circle"></i>
                        <span>FAQ Management</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.contacts.*') ? 'active' : '' }}" 
                       href="{{ route('admin.contacts.index') }}">
                        <i class="fas fa-fw fa-envelope"></i>
                        <span>Contact Messages</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.quotes.*') ? 'active' : '' }}" 
                       href="{{ route('admin.quotes.index') }}">
                        <i class="fas fa-fw fa-file-invoice"></i>
                        <span>Quote Requests</span>
                    </a>
                </li>

                <hr class="sidebar-divider" style="border-color: rgba(255,255,255,0.15);">

                <li class="nav-item">
                    <a class="nav-link {{ request()->routeIs('admin.settings*') ? 'active' : '' }}" 
                       href="{{ route('admin.settings.index') }}">
                        <i class="fas fa-fw fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link" href="{{ route('admin.logout') }}" 
                       onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                        <i class="fas fa-fw fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                    <form id="logout-form" action="{{ route('admin.logout') }}" method="POST" class="d-none">
                        @csrf
                    </form>
                </li>
            </ul>
        </nav>

        <!-- Main Content -->
        <div class="flex-grow-1">
            <!-- Topbar -->
            <nav class="navbar navbar-expand topbar mb-4 static-top shadow">
                <button class="btn btn-link d-md-none rounded-circle me-3" id="sidebarToggle">
                    <i class="fa fa-bars"></i>
                </button>

                <ul class="navbar-nav ms-auto">
                    <!-- User Info -->
                    <li class="nav-item dropdown no-arrow">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                           data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <span class="me-2 d-none d-lg-inline text-gray-600 small">Admin</span>
                            <i class="fas fa-user-circle fa-lg text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end shadow animated--grow-in">
                            <a class="dropdown-item" href="{{ route('admin.settings.index') }}">
                                <i class="fas fa-cogs fa-sm fa-fw me-2 text-gray-400"></i>
                                Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{{ route('admin.logout') }}"
                               onclick="event.preventDefault(); document.getElementById('logout-form-2').submit();">
                                <i class="fas fa-sign-out-alt fa-sm fa-fw me-2 text-gray-400"></i>
                                Logout
                            </a>
                            <form id="logout-form-2" action="{{ route('admin.logout') }}" method="POST" class="d-none">
                                @csrf
                            </form>
                        </div>
                    </li>
                </ul>
            </nav>

            <!-- Page Content -->
            <div class="main-content">
                @yield('content')
            </div>
        </div>
    </div>

    <!-- Sidebar Overlay for Mobile -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Custom scripts -->
    <script>
        $(document).ready(function() {
            // Sidebar toggle for mobile with overlay
            $('#sidebarToggle').click(function(e) {
                e.preventDefault();
                e.stopPropagation();
                toggleSidebar();
            });

            // Close sidebar when clicking overlay
            $('#sidebarOverlay').click(function() {
                closeSidebar();
            });

            // Close sidebar when clicking outside on mobile
            $(document).click(function(e) {
                if ($(window).width() <= 991 && 
                    !$(e.target).closest('#sidebar, #sidebarToggle').length && 
                    $('#sidebar').hasClass('show')) {
                    closeSidebar();
                }
            });

            // Handle window resize
            $(window).resize(function() {
                if ($(window).width() > 991) {
                    closeSidebar();
                }
            });

            // Touch support for mobile sidebar
            let startX = 0;
            let currentX = 0;
            let sidebarWidth = 0;

            $('#sidebar').on('touchstart', function(e) {
                startX = e.originalEvent.touches[0].clientX;
                sidebarWidth = $(this).outerWidth();
            });

            $('#sidebar').on('touchmove', function(e) {
                if ($(window).width() <= 991) {
                    currentX = e.originalEvent.touches[0].clientX;
                    let deltaX = currentX - startX;
                    
                    if (deltaX < -50) { // Swipe left to close
                        closeSidebar();
                    }
                }
            });

            // Prevent body scroll when sidebar is open on mobile
            function toggleBodyScroll(disable) {
                if ($(window).width() <= 991) {
                    if (disable) {
                        $('body').addClass('overflow-hidden');
                    } else {
                        $('body').removeClass('overflow-hidden');
                    }
                }
            }

            // Auto-hide alerts after 5 seconds
            $('.alert').delay(5000).fadeOut();

            // CSRF token setup for AJAX
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            // Smooth scrolling for anchor links
            $('a[href^="#"]').on('click', function(e) {
                e.preventDefault();
                const target = $(this.getAttribute('href'));
                if (target.length) {
                    $('html, body').animate({
                        scrollTop: target.offset().top - 100
                    }, 500);
                }
            });

            // Form validation enhancement
            $('form').on('submit', function() {
                const submitBtn = $(this).find('button[type="submit"]');
                if (submitBtn.length) {
                    showLoading(submitBtn);
                }
            });
        });

        // Sidebar control functions
        function toggleSidebar() {
            const sidebar = $('#sidebar');
            const overlay = $('#sidebarOverlay');
            
            if (sidebar.hasClass('show')) {
                closeSidebar();
            } else {
                openSidebar();
            }
        }

        function openSidebar() {
            $('#sidebar').addClass('show');
            $('#sidebarOverlay').addClass('show');
            $('body').addClass('overflow-hidden');
        }

        function closeSidebar() {
            $('#sidebar').removeClass('show');
            $('#sidebarOverlay').removeClass('show');
            $('body').removeClass('overflow-hidden');
        }

        // Confirm delete actions
        function confirmDelete(message = 'Are you sure you want to delete this item?') {
            return confirm(message);
        }

        // Show loading spinner
        function showLoading(element) {
            $(element).html('<i class="fas fa-spinner fa-spin"></i> Loading...');
            $(element).prop('disabled', true);
        }

        // Hide loading spinner
        function hideLoading(element, originalText) {
            $(element).html(originalText);
            $(element).prop('disabled', false);
        }
    </script>

    @stack('scripts')
</body>
</html>
