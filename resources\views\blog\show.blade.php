@extends('layouts.app')

@section('title', $article['title'] . ' - Agent Marketing Blog')

@section('description', $article['excerpt'])

@section('content')
<!-- Article Header -->
<section class="article-header">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="article-breadcrumb mb-4" data-aos="fade-up">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item">
                                <a href="{{ route('home') }}">
                                    {{ session('locale', 'ar') === 'ar' ? 'الرئيسية' : 'Home' }}
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="{{ route('blog.index') }}">
                                    {{ session('locale', 'ar') === 'ar' ? 'المدونة' : 'Blog' }}
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">
                                {{ $article['title'] }}
                            </li>
                        </ol>
                    </nav>
                </div>
                
                <div class="article-meta mb-4" data-aos="fade-up" data-aos-delay="100">
                    <div class="d-flex flex-wrap align-items-center gap-3">
                        <span class="category-badge">
                            {{ session('locale', 'ar') === 'ar' ? 
                                (collect(['digital-marketing' => 'التسويق الرقمي', 'social-media' => 'وسائل التواصل الاجتماعي', 'seo' => 'تحسين محركات البحث', 'content-marketing' => 'تسويق المحتوى', 'advertising' => 'الإعلانات', 'tips' => 'نصائح وحيل'])->get($article['category'])) : 
                                (collect(['digital-marketing' => 'Digital Marketing', 'social-media' => 'Social Media', 'seo' => 'SEO', 'content-marketing' => 'Content Marketing', 'advertising' => 'Advertising', 'tips' => 'Tips & Tricks'])->get($article['category'])) 
                            }}
                        </span>
                        <span class="author">
                            <i class="fas fa-user me-1"></i>
                            {{ $article['author'] }}
                        </span>
                        <span class="date">
                            <i class="fas fa-calendar me-1"></i>
                            {{ date('F d, Y', strtotime($article['published_at'])) }}
                        </span>
                        <span class="read-time">
                            <i class="fas fa-clock me-1"></i>
                            {{ $article['read_time'] }} {{ session('locale', 'ar') === 'ar' ? 'دقائق قراءة' : 'min read' }}
                        </span>
                    </div>
                </div>
                
                <h1 class="article-title mb-4" data-aos="fade-up" data-aos-delay="200">
                    {{ $article['title'] }}
                </h1>
                
                <p class="article-excerpt lead mb-4" data-aos="fade-up" data-aos-delay="300">
                    {{ $article['excerpt'] }}
                </p>
                
                <div class="article-tags mb-4" data-aos="fade-up" data-aos-delay="400">
                    @foreach($article['tags'] as $tag)
                    <span class="tag">#{{ $tag }}</span>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Article Image -->
<section class="article-image-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-10 mx-auto">
                <div class="article-featured-image" data-aos="fade-up">
                    <img src="{{ $article['image'] }}" alt="{{ $article['title'] }}" class="img-fluid rounded">
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Article Content -->
<section class="article-content-section section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="article-content" data-aos="fade-up">
                    <div class="content-text">
                        <p>{{ $article['content'] }}</p>
                        
                        @if(session('locale', 'ar') === 'ar')
                        <p>في عالم التسويق الرقمي المتطور باستمرار، تحتاج الشركات إلى البقاء على اطلاع دائم بأحدث الاتجاهات والتقنيات. هذا المقال يقدم نظرة شاملة على أهم الاستراتيجيات التي يمكن أن تساعد في تحقيق النجاح.</p>
                        
                        <h3>النقاط الرئيسية</h3>
                        <ul>
                            <li>فهم احتياجات الجمهور المستهدف</li>
                            <li>استخدام البيانات لاتخاذ قرارات مدروسة</li>
                            <li>التركيز على تجربة المستخدم</li>
                            <li>قياس النتائج وتحسين الأداء باستمرار</li>
                        </ul>
                        
                        <p>من خلال تطبيق هذه الاستراتيجيات، يمكن للشركات تحقيق نتائج ملموسة وبناء علاقات قوية مع عملائها.</p>
                        
                        <h3>الخلاصة</h3>
                        <p>النجاح في التسويق الرقمي يتطلب فهماً عميقاً للسوق والجمهور، بالإضافة إلى استخدام الأدوات والتقنيات المناسبة. في Agent Marketing، نساعد عملاءنا على تحقيق أهدافهم من خلال استراتيجيات مخصصة وفعالة.</p>
                        @else
                        <p>In the ever-evolving world of digital marketing, businesses need to stay constantly updated with the latest trends and technologies. This article provides a comprehensive overview of the most important strategies that can help achieve success.</p>
                        
                        <h3>Key Points</h3>
                        <ul>
                            <li>Understanding target audience needs</li>
                            <li>Using data to make informed decisions</li>
                            <li>Focusing on user experience</li>
                            <li>Measuring results and continuously improving performance</li>
                        </ul>
                        
                        <p>By implementing these strategies, companies can achieve tangible results and build strong relationships with their customers.</p>
                        
                        <h3>Conclusion</h3>
                        <p>Success in digital marketing requires a deep understanding of the market and audience, as well as using the right tools and techniques. At Agent Marketing, we help our clients achieve their goals through customized and effective strategies.</p>
                        @endif
                    </div>
                    
                    <!-- Social Share -->
                    <div class="social-share mt-5">
                        <h5 class="mb-3">
                            {{ session('locale', 'ar') === 'ar' ? 'شارك المقال' : 'Share Article' }}
                        </h5>
                        <div class="share-buttons">
                            <a href="#" class="share-btn facebook" onclick="shareOnFacebook()">
                                <i class="fab fa-facebook-f"></i>
                                Facebook
                            </a>
                            <a href="#" class="share-btn twitter" onclick="shareOnTwitter()">
                                <i class="fab fa-twitter"></i>
                                Twitter
                            </a>
                            <a href="#" class="share-btn linkedin" onclick="shareOnLinkedIn()">
                                <i class="fab fa-linkedin-in"></i>
                                LinkedIn
                            </a>
                            <a href="#" class="share-btn whatsapp" onclick="shareOnWhatsApp()">
                                <i class="fab fa-whatsapp"></i>
                                WhatsApp
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Articles -->
@if(count($relatedArticles) > 0)
<section class="related-articles-section section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">
                    {{ session('locale', 'ar') === 'ar' ? 'مقالات ذات صلة' : 'Related Articles' }}
                </h2>
                <p class="text-muted">
                    {{ session('locale', 'ar') === 'ar' ? 'مقالات أخرى قد تهمك' : 'Other articles you might find interesting' }}
                </p>
            </div>
        </div>
        
        <div class="row">
            @foreach($relatedArticles as $relatedArticle)
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                <div class="related-article-card">
                    <div class="article-image">
                        <img src="{{ $relatedArticle['image'] }}" alt="{{ $relatedArticle['title'] }}" class="img-fluid">
                        <div class="article-category">
                            {{ session('locale', 'ar') === 'ar' ? 
                                (collect(['digital-marketing' => 'التسويق الرقمي', 'social-media' => 'وسائل التواصل الاجتماعي', 'seo' => 'تحسين محركات البحث', 'content-marketing' => 'تسويق المحتوى', 'advertising' => 'الإعلانات', 'tips' => 'نصائح وحيل'])->get($relatedArticle['category'])) : 
                                (collect(['digital-marketing' => 'Digital Marketing', 'social-media' => 'Social Media', 'seo' => 'SEO', 'content-marketing' => 'Content Marketing', 'advertising' => 'Advertising', 'tips' => 'Tips & Tricks'])->get($relatedArticle['category'])) 
                            }}
                        </div>
                    </div>
                    <div class="article-content">
                        <div class="article-meta">
                            <span class="date">
                                <i class="fas fa-calendar me-1"></i>
                                {{ date('M d', strtotime($relatedArticle['published_at'])) }}
                            </span>
                            <span class="read-time">
                                <i class="fas fa-clock me-1"></i>
                                {{ $relatedArticle['read_time'] }}{{ session('locale', 'ar') === 'ar' ? 'د' : 'm' }}
                            </span>
                        </div>
                        <h4 class="article-title">
                            <a href="{{ route('blog.show', $relatedArticle['slug']) }}">{{ $relatedArticle['title'] }}</a>
                        </h4>
                        <p class="article-excerpt">{{ $relatedArticle['excerpt'] }}</p>
                        <a href="{{ route('blog.show', $relatedArticle['slug']) }}" class="read-more">
                            {{ session('locale', 'ar') === 'ar' ? 'اقرأ المزيد' : 'Read More' }}
                            <i class="fas fa-arrow-{{ session('locale', 'ar') === 'ar' ? 'left' : 'right' }} ms-1"></i>
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- CTA Section -->
<section class="section-padding bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <h2 class="mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        هل تحتاج مساعدة في تطبيق هذه الاستراتيجيات؟
                    @else
                        Need Help Implementing These Strategies?
                    @endif
                </h2>
                <p class="lead mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        فريقنا من الخبراء جاهز لمساعدتك في تحقيق أهدافك التسويقية
                    @else
                        Our team of experts is ready to help you achieve your marketing goals
                    @endif
                </p>
                <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                    <a href="{{ route('quote') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-rocket me-2"></i>
                        {{ session('locale', 'ar') === 'ar' ? 'احصل على عرض سعر' : 'Get Quote' }}
                    </a>
                    <a href="{{ route('contact') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-calendar me-2"></i>
                        {{ session('locale', 'ar') === 'ar' ? 'احجز استشارة مجانية' : 'Book Free Consultation' }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.article-header {
    padding: 2rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: #6c757d;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #007bff;
}

.breadcrumb-item.active {
    color: #212529;
    font-weight: 500;
}

.article-meta {
    font-size: 0.9rem;
    color: #6c757d;
}

.category-badge {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: 500;
}

.article-title {
    font-size: 2.5rem;
    font-weight: bold;
    line-height: 1.2;
    color: #212529;
}

.article-excerpt {
    font-size: 1.2rem;
    color: #6c757d;
    line-height: 1.6;
}

.article-tags .tag {
    background: #e9ecef;
    color: #6c757d;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    display: inline-block;
}

.article-image-section {
    padding: 2rem 0;
}

.article-featured-image {
    text-align: center;
}

.article-featured-image img {
    max-height: 500px;
    width: 100%;
    object-fit: cover;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
}

.article-content-section {
    padding: 3rem 0;
}

.content-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
}

.content-text h3 {
    color: #212529;
    font-weight: bold;
    margin: 2rem 0 1rem;
}

.content-text ul {
    padding-left: 2rem;
    margin: 1.5rem 0;
}

.content-text li {
    margin-bottom: 0.5rem;
}

.social-share {
    border-top: 1px solid #e9ecef;
    padding-top: 2rem;
}

.share-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.share-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    color: white;
}

.share-btn.facebook {
    background: #1877f2;
}

.share-btn.twitter {
    background: #1da1f2;
}

.share-btn.linkedin {
    background: #0077b5;
}

.share-btn.whatsapp {
    background: #25d366;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: white;
}

.related-articles-section {
    padding: 4rem 0;
}

.related-article-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
}

.related-article-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.related-article-card .article-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.related-article-card .article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.related-article-card:hover .article-image img {
    transform: scale(1.05);
}

.related-article-card .article-category {
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.related-article-card .article-content {
    padding: 1.5rem;
}

.related-article-card .article-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.8rem;
    color: #6c757d;
}

.related-article-card .article-title {
    font-size: 1.1rem;
    margin-bottom: 1rem;
}

.related-article-card .article-title a {
    color: #212529;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.related-article-card .article-title a:hover {
    color: #007bff;
}

.related-article-card .article-excerpt {
    color: #6c757d;
    line-height: 1.5;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.related-article-card .read-more {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.related-article-card .read-more:hover {
    color: #0056b3;
}

@media (max-width: 768px) {
    .article-title {
        font-size: 2rem;
    }
    
    .article-excerpt {
        font-size: 1.1rem;
    }
    
    .content-text {
        font-size: 1rem;
    }
    
    .share-buttons {
        flex-direction: column;
    }
    
    .share-btn {
        justify-content: center;
    }
    
    .article-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
}
</style>

<script>
// Social sharing functions
function shareOnFacebook() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(document.title);
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank', 'width=600,height=400');
}

function shareOnTwitter() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(document.title);
    window.open(`https://twitter.com/intent/tweet?url=${url}&text=${title}`, '_blank', 'width=600,height=400');
}

function shareOnLinkedIn() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(document.title);
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank', 'width=600,height=400');
}

function shareOnWhatsApp() {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(document.title);
    window.open(`https://wa.me/?text=${title} ${url}`, '_blank');
}
</script>
@endsection
