<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    public function index()
    {
        $contactInfo = [
            'phone' => '+966 50 123 4567',
            'email' => '<EMAIL>',
            'address' => session('locale', 'ar') === 'ar' ? 'الرياض، المملكة العربية السعودية' : 'Riyadh, Saudi Arabia',
            'hours' => session('locale', 'ar') === 'ar' ? 'الأحد - الخميس: 9:00 - 18:00' : 'Sun - Thu: 9:00 AM - 6:00 PM'
        ];

        $services = [
            'ad-campaigns' => session('locale', 'ar') === 'ar' ? 'إدارة الحملات الإعلانية' : 'Ad Campaign Management',
            'social-media' => session('locale', 'ar') === 'ar' ? 'إدارة وسائل التواصل الاجتماعي' : 'Social Media Management',
            'seo' => session('locale', 'ar') === 'ar' ? 'تحسين محركات البحث' : 'SEO Optimization',
            'branding' => session('locale', 'ar') === 'ar' ? 'تصميم الهوية البصرية' : 'Brand Identity Design',
            'web-development' => session('locale', 'ar') === 'ar' ? 'تطوير المواقع الإلكترونية' : 'Web Development',
            'content-marketing' => session('locale', 'ar') === 'ar' ? 'تسويق المحتوى' : 'Content Marketing'
        ];

        return view('contact', compact('contactInfo', 'services'));
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'company' => 'nullable|string|max:255',
            'service' => 'required|string',
            'message' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Here you would typically send an email or save to database
        // For now, we'll just return a success message
        
        $successMessage = session('locale', 'ar') === 'ar' 
            ? 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.'
            : 'Your message has been sent successfully! We will contact you soon.';

        return back()->with('success', $successMessage);
    }

    public function quote(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'required|string|max:20',
            'company' => 'required|string|max:255',
            'services' => 'required|array|min:1',
            'budget' => 'required|string',
            'timeline' => 'required|string',
            'description' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Here you would typically send an email or save to database
        // For now, we'll just return a success message
        
        $successMessage = session('locale', 'ar') === 'ar' 
            ? 'تم إرسال طلب العرض بنجاح! سنرسل لك عرض سعر مفصل خلال 24 ساعة.'
            : 'Quote request sent successfully! We will send you a detailed quote within 24 hours.';

        return back()->with('success', $successMessage);
    }
}
