@if(count($faqs) > 0)
<div class="table-responsive">
    <table class="table table-hover">
        <thead>
            <tr>
                <th width="50">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="selectAll">
                        <label class="form-check-label" for="selectAll"></label>
                    </div>
                </th>
                <th>السؤال</th>
                <th>الفئة</th>
                <th>الحالة</th>
                <th>المشاهدات</th>
                <th>الترتيب</th>
                <th>تاريخ الإنشاء</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody id="sortable-faqs">
            @foreach($faqs as $faq)
            <tr class="faq-item" 
                data-id="{{ $faq['id'] }}"
                data-question="{{ strtolower($faq['question']) }}" 
                data-category="{{ $faq['category'] }}" 
                data-status="{{ $faq['status'] }}">
                <td>
                    <div class="form-check">
                        <input class="form-check-input faq-checkbox" 
                               type="checkbox" 
                               value="{{ $faq['id'] }}" 
                               id="faq_{{ $faq['id'] }}">
                        <label class="form-check-label" for="faq_{{ $faq['id'] }}"></label>
                    </div>
                </td>
                <td>
                    <div class="d-flex align-items-start">
                        <div class="drag-handle me-2 text-muted" style="cursor: move;">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">{{ Str::limit($faq['question'], 60) }}</h6>
                            <small class="text-muted">ID: {{ $faq['id'] }}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="badge bg-{{ $faq['category'] === 'general' ? 'primary' : ($faq['category'] === 'services' ? 'success' : ($faq['category'] === 'pricing' ? 'warning' : ($faq['category'] === 'technical' ? 'info' : 'secondary'))) }}">
                        @switch($faq['category'])
                            @case('general')
                                عام
                                @break
                            @case('services')
                                الخدمات
                                @break
                            @case('pricing')
                                الأسعار
                                @break
                            @case('technical')
                                تقني
                                @break
                            @case('support')
                                الدعم
                                @break
                            @default
                                {{ $faq['category'] }}
                        @endswitch
                    </span>
                </td>
                <td>
                    <div class="form-check form-switch">
                        <input class="form-check-input" 
                               type="checkbox" 
                               {{ $faq['status'] === 'active' ? 'checked' : '' }}
                               onchange="toggleStatus({{ $faq['id'] }}, '{{ $faq['status'] }}')"
                               id="status_{{ $faq['id'] }}">
                        <label class="form-check-label" for="status_{{ $faq['id'] }}">
                            <span class="badge bg-{{ $faq['status'] === 'active' ? 'success' : 'secondary' }}">
                                {{ $faq['status'] === 'active' ? 'نشط' : 'غير نشط' }}
                            </span>
                        </label>
                    </div>
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-eye text-muted me-1"></i>
                        {{ number_format($faq['views']) }}
                    </div>
                </td>
                <td>
                    <span class="badge bg-light text-dark">{{ $faq['order'] }}</span>
                </td>
                <td>
                    <small class="text-muted">
                        {{ date('Y/m/d', strtotime($faq['created_at'])) }}
                    </small>
                </td>
                <td>
                    <div class="btn-group" role="group">
                        <a href="{{ route('admin.faq.edit', $faq['id']) }}" 
                           class="btn btn-sm btn-outline-primary" 
                           title="تعديل">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button type="button" 
                                class="btn btn-sm btn-outline-info" 
                                onclick="viewFAQ({{ $faq['id'] }})"
                                title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button type="button" 
                                class="btn btn-sm btn-outline-danger" 
                                onclick="deleteFAQ({{ $faq['id'] }})"
                                title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
</div>

<!-- Pagination would go here in a real application -->
<div class="d-flex justify-content-between align-items-center mt-3">
    <div class="text-muted">
        عرض {{ count($faqs) }} من {{ count($faqs) }} سؤال
    </div>
    <div>
        <!-- Pagination links would be here -->
    </div>
</div>

@else
<div class="text-center py-5">
    <i class="fas fa-question-circle fa-3x text-muted mb-3"></i>
    <h5 class="text-muted">لا توجد أسئلة في هذه الفئة</h5>
    <p class="text-muted">ابدأ بإضافة سؤال جديد</p>
    <a href="{{ route('admin.faq.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        إضافة سؤال جديد
    </a>
</div>
@endif

<!-- FAQ View Modal -->
<div class="modal fade" id="faqViewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">عرض السؤال</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="faqViewContent">
                    <!-- FAQ content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" id="editFromModal">تعديل</button>
            </div>
        </div>
    </div>
</div>

<script>
// View FAQ function
function viewFAQ(faqId) {
    // In a real application, this would fetch FAQ data via AJAX
    const faqData = @json($faqs).find(faq => faq.id == faqId);
    
    if (faqData) {
        const content = `
            <div class="mb-3">
                <h6 class="text-primary">السؤال:</h6>
                <p>${faqData.question}</p>
            </div>
            <div class="mb-3">
                <h6 class="text-primary">الإجابة:</h6>
                <div>${faqData.answer}</div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-primary">الفئة:</h6>
                    <span class="badge bg-primary">${getCategoryName(faqData.category)}</span>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">الحالة:</h6>
                    <span class="badge bg-${faqData.status === 'active' ? 'success' : 'secondary'}">
                        ${faqData.status === 'active' ? 'نشط' : 'غير نشط'}
                    </span>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-6">
                    <h6 class="text-primary">المشاهدات:</h6>
                    <p>${faqData.views.toLocaleString()}</p>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary">الترتيب:</h6>
                    <p>${faqData.order}</p>
                </div>
            </div>
        `;
        
        document.getElementById('faqViewContent').innerHTML = content;
        document.getElementById('editFromModal').onclick = () => {
            window.location.href = `/admin/faq/${faqId}/edit`;
        };
        
        const modal = new bootstrap.Modal(document.getElementById('faqViewModal'));
        modal.show();
    }
}

function getCategoryName(category) {
    const categories = {
        'general': 'عام',
        'services': 'الخدمات',
        'pricing': 'الأسعار',
        'technical': 'تقني',
        'support': 'الدعم'
    };
    return categories[category] || category;
}
</script>
