@extends('admin.layout')

@section('title', 'إعدادات الموقع')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-cog me-2"></i>
                    إعدادات الموقع
                </h1>
            </div>

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i>
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            @endif

            <div class="row">
                <!-- General Settings -->
                <div class="col-lg-8">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-globe me-2"></i>
                                الإعدادات العامة
                            </h6>
                        </div>
                        <div class="card-body">
                            <form action="{{ route('admin.settings.update') }}" method="POST">
                                @csrf
                                
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="site_name_ar" class="form-label">اسم الموقع (عربي)</label>
                                        <input type="text" class="form-control" id="site_name_ar" name="site_name_ar" 
                                               value="{{ $settings['site_name_ar'] }}" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="site_name_en" class="form-label">اسم الموقع (إنجليزي)</label>
                                        <input type="text" class="form-control" id="site_name_en" name="site_name_en" 
                                               value="{{ $settings['site_name_en'] }}" required>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="contact_email" class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control" id="contact_email" name="contact_email" 
                                               value="{{ $settings['contact_email'] }}" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="contact_phone" class="form-label">رقم الهاتف</label>
                                        <input type="text" class="form-control" id="contact_phone" name="contact_phone" 
                                               value="{{ $settings['contact_phone'] }}" required>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="whatsapp_number" class="form-label">رقم الواتساب</label>
                                        <input type="text" class="form-control" id="whatsapp_number" name="whatsapp_number" 
                                               value="{{ $settings['whatsapp_number'] }}" required>
                                    </div>
                                </div>

                                <hr class="my-4">

                                <h6 class="font-weight-bold text-primary mb-3">
                                    <i class="fab fa-facebook me-2"></i>
                                    روابط وسائل التواصل الاجتماعي
                                </h6>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="facebook_url" class="form-label">
                                            <i class="fab fa-facebook text-primary me-1"></i>
                                            فيسبوك
                                        </label>
                                        <input type="url" class="form-control" id="facebook_url" name="facebook_url" 
                                               value="{{ $settings['facebook_url'] ?? '' }}" placeholder="https://facebook.com/yourpage">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="twitter_url" class="form-label">
                                            <i class="fab fa-twitter text-info me-1"></i>
                                            تويتر
                                        </label>
                                        <input type="url" class="form-control" id="twitter_url" name="twitter_url" 
                                               value="{{ $settings['twitter_url'] ?? '' }}" placeholder="https://twitter.com/yourhandle">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="instagram_url" class="form-label">
                                            <i class="fab fa-instagram text-danger me-1"></i>
                                            إنستغرام
                                        </label>
                                        <input type="url" class="form-control" id="instagram_url" name="instagram_url" 
                                               value="{{ $settings['instagram_url'] ?? '' }}" placeholder="https://instagram.com/yourhandle">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="linkedin_url" class="form-label">
                                            <i class="fab fa-linkedin text-primary me-1"></i>
                                            لينكد إن
                                        </label>
                                        <input type="url" class="form-control" id="linkedin_url" name="linkedin_url" 
                                               value="{{ $settings['linkedin_url'] ?? '' }}" placeholder="https://linkedin.com/company/yourcompany">
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>
                                        حفظ الإعدادات
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="col-lg-4">
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-tools me-2"></i>
                                إجراءات سريعة
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <a href="{{ route('admin.blog.index') }}" class="list-group-item list-group-item-action">
                                    <i class="fas fa-blog text-primary me-2"></i>
                                    إدارة المقالات
                                </a>
                                <a href="{{ route('admin.faq.index') }}" class="list-group-item list-group-item-action">
                                    <i class="fas fa-question-circle text-info me-2"></i>
                                    إدارة الأسئلة الشائعة
                                </a>
                                <a href="{{ route('admin.contacts.index') }}" class="list-group-item list-group-item-action">
                                    <i class="fas fa-envelope text-success me-2"></i>
                                    رسائل الاتصال
                                </a>
                                <a href="{{ route('admin.quotes.index') }}" class="list-group-item list-group-item-action">
                                    <i class="fas fa-file-invoice text-warning me-2"></i>
                                    طلبات العروض
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- System Info -->
                    <div class="card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">
                                <i class="fas fa-info-circle me-2"></i>
                                معلومات النظام
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="border-end">
                                        <div class="h5 font-weight-bold text-primary">{{ \App::version() }}</div>
                                        <div class="small text-muted">إصدار Laravel</div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="h5 font-weight-bold text-success">{{ PHP_VERSION }}</div>
                                    <div class="small text-muted">إصدار PHP</div>
                                </div>
                            </div>
                            <hr>
                            <div class="text-center">
                                <small class="text-muted">
                                    آخر تحديث: {{ date('Y-m-d H:i') }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.list-group-item-action:hover {
    background-color: #f8f9fc;
}
</style>
@endsection
