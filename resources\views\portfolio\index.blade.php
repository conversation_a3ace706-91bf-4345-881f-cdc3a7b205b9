@extends('layouts.app')

@section('title', 'Our Portfolio - Agent Marketing | Digital Marketing Case Studies & Success Stories')
@section('description', 'Explore our portfolio of successful digital marketing projects. See real results from our SEO, social media, advertising, and branding campaigns for clients across various industries.')
@section('keywords', 'digital marketing portfolio, case studies, marketing success stories, SEO results, social media campaigns, advertising portfolio, branding projects, marketing agency work')
@section('og_title', 'Agent Marketing Portfolio - Proven Digital Marketing Results')
@section('og_description', 'Discover our successful digital marketing projects and the measurable results we achieved for our clients. From SEO to social media and advertising campaigns.')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center" style="min-height: 60vh;">
            <div class="col-lg-6" data-aos="fade-right">
                <h1 class="display-4 fw-bold mb-4">
                    Our Work & Projects
                </h1>
                <p class="lead mb-4">
                    Discover our best work and successful projects that achieved exceptional results for our clients across various industries
                </p>
                <div class="d-flex align-items-center">
                    <div class="bg-success text-white rounded-circle p-3 me-3">
                        <i class="fas fa-trophy fs-4"></i>
                    </div>
                    <div>
                        <strong>
                            Over 500 Successful Projects
                        </strong>
                    </div>
                </div>
            </div>
            <div class="col-lg-6" data-aos="fade-left">
                <div class="text-center">
                    <i class="fas fa-briefcase text-white" style="font-size: 8rem; opacity: 0.8;"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Portfolio Filter -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">
                    Our Featured Projects
                </h2>
                <p class="text-muted">
                    Browse our best work categorized by service type
                </p>
            </div>
        </div>
        
        <!-- Filter Buttons -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="portfolio-filter text-center" data-aos="fade-up">
                    @foreach($categories as $key => $category)
                    <button class="filter-btn {{ $key === 'all' ? 'active' : '' }}" data-filter="{{ $key }}">
                        {{ $category }}
                    </button>
                    @endforeach
                </div>
            </div>
        </div>
        
        <!-- Portfolio Grid -->
        <div class="row portfolio-grid">
            @foreach($projects as $project)
            <div class="col-lg-4 col-md-6 mb-4 portfolio-item" data-category="{{ $project['category'] }}" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                <div class="portfolio-card">
                    <div class="portfolio-image">
                        <div class="portfolio-placeholder">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="portfolio-overlay">
                            <div class="portfolio-overlay-content">
                                <h5 class="text-white mb-2">{{ $project['title'] }}</h5>
                                <p class="text-white-50 mb-3">{{ $project['client'] }}</p>
                                <button class="btn btn-light btn-sm" onclick="openProjectModal({{ $project['id'] }})">
                                    <i class="fas fa-eye me-2"></i>
                                    {{ session('locale', 'ar') === 'ar' ? 'عرض التفاصيل' : 'View Details' }}
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="portfolio-content">
                        <div class="portfolio-category">
                            <span class="badge bg-primary">{{ $categories[$project['category']] }}</span>
                            <span class="portfolio-year">{{ $project['year'] }}</span>
                        </div>
                        <h5 class="portfolio-title">{{ $project['title'] }}</h5>
                        <p class="portfolio-description">{{ Str::limit($project['description'], 100) }}</p>
                        <div class="portfolio-results">
                            <small class="text-muted">
                                <i class="fas fa-chart-line me-1"></i>
                                {{ session('locale', 'ar') === 'ar' ? 'النتائج الرئيسية:' : 'Key Results:' }}
                            </small>
                            <ul class="results-list">
                                @foreach(array_slice($project['results'], 0, 2) as $result)
                                <li>{{ $result }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Success Stats -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">
                    Proven & Measurable Results
                </h2>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up">
                <div class="success-stat text-center">
                    <div class="stat-icon bg-primary mb-3">
                        <i class="fas fa-chart-line text-white"></i>
                    </div>
                    <h3 class="stat-number">250%</h3>
                    <p class="stat-label">{{ session('locale', 'ar') === 'ar' ? 'متوسط زيادة المبيعات' : 'Average Sales Increase' }}</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="success-stat text-center">
                    <div class="stat-icon bg-success mb-3">
                        <i class="fas fa-users text-white"></i>
                    </div>
                    <h3 class="stat-number">180%</h3>
                    <p class="stat-label">{{ session('locale', 'ar') === 'ar' ? 'متوسط زيادة الوصول' : 'Average Reach Increase' }}</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="success-stat text-center">
                    <div class="stat-icon bg-warning mb-3">
                        <i class="fas fa-mouse-pointer text-white"></i>
                    </div>
                    <h3 class="stat-number">15%</h3>
                    <p class="stat-label">{{ session('locale', 'ar') === 'ar' ? 'متوسط معدل التحويل' : 'Average Conversion Rate' }}</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="success-stat text-center">
                    <div class="stat-icon bg-info mb-3">
                        <i class="fas fa-clock text-white"></i>
                    </div>
                    <h3 class="stat-number">4.2</h3>
                    <p class="stat-label">{{ session('locale', 'ar') === 'ar' ? 'متوسط مدة المشروع (أشهر)' : 'Average Project Duration (Months)' }}</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section-padding bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <h2 class="mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        هل أنت مستعد لتكون قصة نجاحنا التالية؟
                    @else
                        Are You Ready to Be Our Next Success Story?
                    @endif
                </h2>
                <p class="lead mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        دعنا نساعدك في تحقيق نتائج مماثلة أو أفضل لمشروعك
                    @else
                        Let us help you achieve similar or better results for your project
                    @endif
                </p>
                <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                    <a href="{{ route('quote') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-rocket me-2"></i>
                        {{ session('locale', 'ar') === 'ar' ? 'ابدأ مشروعك الآن' : 'Start Your Project Now' }}
                    </a>
                    <a href="{{ route('contact') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-comments me-2"></i>
                        {{ session('locale', 'ar') === 'ar' ? 'تحدث مع خبير' : 'Talk to an Expert' }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Project Modal -->
<div class="modal fade" id="projectModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="projectModalTitle"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="projectModalBody">
                <!-- Project details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ session('locale', 'ar') === 'ar' ? 'إغلاق' : 'Close' }}
                </button>
                <a href="{{ route('quote') }}" class="btn btn-primary">
                    <i class="fas fa-paper-plane me-2"></i>
                    {{ session('locale', 'ar') === 'ar' ? 'احصل على عرض سعر' : 'Get Quote' }}
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.portfolio-filter {
    margin-bottom: 3rem;
}

.filter-btn {
    background: white;
    border: 2px solid #e9ecef;
    color: #6c757d;
    padding: 0.75rem 1.5rem;
    margin: 0.25rem;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.filter-btn:hover,
.filter-btn.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
    transform: translateY(-2px);
}

.portfolio-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.portfolio-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.portfolio-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.portfolio-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 123, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.portfolio-card:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-overlay-content {
    text-align: center;
    padding: 2rem;
}

.portfolio-content {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.portfolio-category {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.portfolio-year {
    color: #6c757d;
    font-size: 0.9rem;
}

.portfolio-title {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.portfolio-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 1rem;
    flex-grow: 1;
}

.portfolio-results {
    margin-top: auto;
}

.results-list {
    list-style: none;
    padding: 0;
    margin: 0.5rem 0 0;
}

.results-list li {
    font-size: 0.85rem;
    color: #28a745;
    margin-bottom: 0.25rem;
    position: relative;
    padding-left: 1rem;
}

.results-list li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #28a745;
    font-weight: bold;
}

.success-stat {
    padding: 2rem 1rem;
}

.stat-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 2rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: bold;
    color: #212529;
    margin: 1rem 0 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 1rem;
    margin: 0;
}

.portfolio-item {
    transition: all 0.3s ease;
}

.portfolio-item.hidden {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

@media (max-width: 768px) {
    .portfolio-card {
        margin-bottom: 2rem;
    }
    
    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .portfolio-image {
        height: 200px;
    }
    
    .stat-number {
        font-size: 2.5rem;
    }
}
</style>

<script>
// Portfolio filtering
document.addEventListener('DOMContentLoaded', function() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    const portfolioItems = document.querySelectorAll('.portfolio-item');
    
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Filter items
            portfolioItems.forEach(item => {
                const category = item.getAttribute('data-category');
                
                if (filter === 'all' || category === filter) {
                    item.classList.remove('hidden');
                } else {
                    item.classList.add('hidden');
                }
            });
        });
    });
});

// Project modal
function openProjectModal(projectId) {
    const projects = @json($projects);
    const project = projects.find(p => p.id === projectId);
    
    if (project) {
        document.getElementById('projectModalTitle').textContent = project.title;
        
        const modalBody = document.getElementById('projectModalBody');
        modalBody.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <div class="project-placeholder mb-3">
                        <i class="fas fa-image"></i>
                    </div>
                </div>
                <div class="col-md-6">
                    <h6 class="text-primary mb-2">${'{{ session("locale", "ar") === "ar" ? "العميل" : "Client" }}'}</h6>
                    <p class="mb-3">${project.client}</p>
                    
                    <h6 class="text-primary mb-2">${'{{ session("locale", "ar") === "ar" ? "المدة" : "Duration" }}'}</h6>
                    <p class="mb-3">${project.duration}</p>
                    
                    <h6 class="text-primary mb-2">${'{{ session("locale", "ar") === "ar" ? "السنة" : "Year" }}'}</h6>
                    <p class="mb-3">${project.year}</p>
                </div>
            </div>
            
            <div class="mt-4">
                <h6 class="text-primary mb-2">${'{{ session("locale", "ar") === "ar" ? "وصف المشروع" : "Project Description" }}'}</h6>
                <p>${project.description}</p>
            </div>
            
            <div class="mt-4">
                <h6 class="text-primary mb-3">${'{{ session("locale", "ar") === "ar" ? "النتائج المحققة" : "Achieved Results" }}'}</h6>
                <div class="row">
                    ${project.results.map(result => `
                        <div class="col-md-6 mb-2">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle text-success me-2"></i>
                                <span>${result}</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
        
        const modal = new bootstrap.Modal(document.getElementById('projectModal'));
        modal.show();
    }
}
</script>

<style>
.project-placeholder {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    border-radius: 10px;
}
</style>
@endsection
