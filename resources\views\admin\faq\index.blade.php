@extends('admin.layouts.app')

@section('title', 'إدارة الأسئلة الشائعة')
@section('page-title', 'إدارة الأسئلة الشائعة')

@section('content')
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">الرئيسية</a></li>
                <li class="breadcrumb-item active">إدارة الأسئلة الشائعة</li>
            </ol>
        </nav>
    </div>
    <a href="{{ route('admin.faq.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        إضافة سؤال جديد
    </a>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-question-circle fa-2x text-primary"></i>
                    </div>
                </div>
                <h4 class="fw-bold text-primary">{{ count($faqs) }}</h4>
                <p class="text-muted mb-0">إجمالي الأسئلة</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="100">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <div class="bg-success bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                </div>
                <h4 class="fw-bold text-success">{{ collect($faqs)->where('status', 'active')->count() }}</h4>
                <p class="text-muted mb-0">أسئلة نشطة</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="200">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <div class="bg-info bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-layer-group fa-2x text-info"></i>
                    </div>
                </div>
                <h4 class="fw-bold text-info">{{ collect($faqs)->pluck('category')->unique()->count() }}</h4>
                <p class="text-muted mb-0">الفئات</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="300">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-eye fa-2x text-warning"></i>
                    </div>
                </div>
                <h4 class="fw-bold text-warning">{{ collect($faqs)->sum('views') }}</h4>
                <p class="text-muted mb-0">إجمالي المشاهدات</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="card mb-4" data-aos="fade-up" data-aos-delay="400">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث في الأسئلة...">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="categoryFilter">
                    <option value="">جميع الفئات</option>
                    <option value="general">عام</option>
                    <option value="services">الخدمات</option>
                    <option value="pricing">الأسعار</option>
                    <option value="technical">تقني</option>
                    <option value="support">الدعم</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- FAQ Categories Tabs -->
<div class="card mb-4" data-aos="fade-up" data-aos-delay="500">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" id="faqTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab">
                    <i class="fas fa-list me-2"></i>جميع الأسئلة
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                    <i class="fas fa-info-circle me-2"></i>عام
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="services-tab" data-bs-toggle="tab" data-bs-target="#services" type="button" role="tab">
                    <i class="fas fa-cogs me-2"></i>الخدمات
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="pricing-tab" data-bs-toggle="tab" data-bs-target="#pricing" type="button" role="tab">
                    <i class="fas fa-dollar-sign me-2"></i>الأسعار
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="technical-tab" data-bs-toggle="tab" data-bs-target="#technical" type="button" role="tab">
                    <i class="fas fa-code me-2"></i>تقني
                </button>
            </li>
        </ul>
    </div>
    <div class="card-body">
        <div class="tab-content" id="faqTabContent">
            <div class="tab-pane fade show active" id="all" role="tabpanel">
                @include('admin.faq.partials.faq-list', ['faqs' => $faqs])
            </div>
            <div class="tab-pane fade" id="general" role="tabpanel">
                @include('admin.faq.partials.faq-list', ['faqs' => collect($faqs)->where('category', 'general')->all()])
            </div>
            <div class="tab-pane fade" id="services" role="tabpanel">
                @include('admin.faq.partials.faq-list', ['faqs' => collect($faqs)->where('category', 'services')->all()])
            </div>
            <div class="tab-pane fade" id="pricing" role="tabpanel">
                @include('admin.faq.partials.faq-list', ['faqs' => collect($faqs)->where('category', 'pricing')->all()])
            </div>
            <div class="tab-pane fade" id="technical" role="tabpanel">
                @include('admin.faq.partials.faq-list', ['faqs' => collect($faqs)->where('category', 'technical')->all()])
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions -->
<div class="card mb-4" data-aos="fade-up" data-aos-delay="600">
    <div class="card-header">
        <h5><i class="fas fa-tasks me-2"></i>الإجراءات المجمعة</h5>
    </div>
    <div class="card-body">
        <form id="bulkActionForm" method="POST" action="{{ route('admin.faq.bulk-action') }}">
            @csrf
            <div class="row align-items-center">
                <div class="col-md-4">
                    <select class="form-select" name="bulk_action" required>
                        <option value="">اختر الإجراء</option>
                        <option value="activate">تفعيل المحدد</option>
                        <option value="deactivate">إلغاء تفعيل المحدد</option>
                        <option value="delete">حذف المحدد</option>
                        <option value="change_category">تغيير الفئة</option>
                    </select>
                </div>
                <div class="col-md-3" id="categorySelect" style="display: none;">
                    <select class="form-select" name="new_category">
                        <option value="general">عام</option>
                        <option value="services">الخدمات</option>
                        <option value="pricing">الأسعار</option>
                        <option value="technical">تقني</option>
                        <option value="support">الدعم</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-warning" disabled id="bulkActionBtn">
                        <i class="fas fa-play me-2"></i>
                        تنفيذ الإجراء
                    </button>
                </div>
                <div class="col-md-2">
                    <span id="selectedCount" class="text-muted">0 محدد</span>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذا السؤال؟ لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Delete Confirmation Modal -->
<div class="modal fade" id="bulkDeleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف المجمع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف <span id="bulkDeleteCount"></span> سؤال؟ لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmBulkDelete">حذف الكل</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Search and Filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const categoryFilter = document.getElementById('categoryFilter');
    const statusFilter = document.getElementById('statusFilter');
    
    function filterFAQs() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedCategory = categoryFilter.value;
        const selectedStatus = statusFilter.value;
        
        document.querySelectorAll('.faq-item').forEach(item => {
            const question = item.dataset.question.toLowerCase();
            const category = item.dataset.category;
            const status = item.dataset.status;
            
            const matchesSearch = question.includes(searchTerm);
            const matchesCategory = !selectedCategory || category === selectedCategory;
            const matchesStatus = !selectedStatus || status === selectedStatus;
            
            if (matchesSearch && matchesCategory && matchesStatus) {
                item.style.display = '';
            } else {
                item.style.display = 'none';
            }
        });
    }
    
    searchInput.addEventListener('input', filterFAQs);
    categoryFilter.addEventListener('change', filterFAQs);
    statusFilter.addEventListener('change', filterFAQs);
});

// Delete FAQ function
function deleteFAQ(faqId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/admin/faq/${faqId}`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Bulk actions functionality
document.addEventListener('DOMContentLoaded', function() {
    const bulkActionSelect = document.querySelector('select[name="bulk_action"]');
    const categorySelect = document.getElementById('categorySelect');
    const bulkActionBtn = document.getElementById('bulkActionBtn');
    const selectedCount = document.getElementById('selectedCount');
    const bulkActionForm = document.getElementById('bulkActionForm');
    
    // Show/hide category select based on action
    bulkActionSelect.addEventListener('change', function() {
        if (this.value === 'change_category') {
            categorySelect.style.display = 'block';
        } else {
            categorySelect.style.display = 'none';
        }
        updateBulkActionButton();
    });
    
    // Handle checkbox selection
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('faq-checkbox')) {
            updateBulkActionButton();
        }
    });
    
    // Select all checkbox
    document.getElementById('selectAll')?.addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.faq-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActionButton();
    });
    
    function updateBulkActionButton() {
        const selectedCheckboxes = document.querySelectorAll('.faq-checkbox:checked');
        const count = selectedCheckboxes.length;
        
        selectedCount.textContent = `${count} محدد`;
        bulkActionBtn.disabled = count === 0 || !bulkActionSelect.value;
    }
    
    // Handle bulk action form submission
    bulkActionForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const selectedCheckboxes = document.querySelectorAll('.faq-checkbox:checked');
        const action = bulkActionSelect.value;
        
        if (selectedCheckboxes.length === 0) {
            alert('يرجى تحديد عنصر واحد على الأقل');
            return;
        }
        
        // Add selected IDs to form
        selectedCheckboxes.forEach(checkbox => {
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'selected_ids[]';
            hiddenInput.value = checkbox.value;
            this.appendChild(hiddenInput);
        });
        
        if (action === 'delete') {
            // Show bulk delete confirmation
            document.getElementById('bulkDeleteCount').textContent = selectedCheckboxes.length;
            const bulkDeleteModal = new bootstrap.Modal(document.getElementById('bulkDeleteModal'));
            bulkDeleteModal.show();
            
            document.getElementById('confirmBulkDelete').onclick = () => {
                this.submit();
            };
        } else {
            // Submit form directly for other actions
            this.submit();
        }
    });
});

// Toggle FAQ status
function toggleStatus(faqId, currentStatus) {
    const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
    
    fetch(`/admin/faq/${faqId}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ status: newStatus })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ أثناء تحديث الحالة');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تحديث الحالة');
    });
}

// Sortable functionality for reordering FAQs
document.addEventListener('DOMContentLoaded', function() {
    // This would require a library like SortableJS for drag-and-drop reordering
    // Implementation would go here
});
</script>
@endpush
