@extends('layouts.app')

@section('title', session('locale', 'ar') === 'ar' ? 'الأسئلة الشائعة - Agent Marketing' : 'FAQ - Agent Marketing')

@section('description', session('locale', 'ar') === 'ar' ? 'إجابات على أكثر الأسئلة شيوعاً حول خدماتنا التسويقية وعملية العمل معنا' : 'Answers to the most frequently asked questions about our marketing services and work process')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center" style="min-height: 60vh;">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <h1 class="display-4 fw-bold mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        الأسئلة الشائعة
                    @else
                        Frequently Asked Questions
                    @endif
                </h1>
                <p class="lead mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        إجابات شاملة على أكثر الأسئلة شيوعاً حول خدماتنا التسويقية وعملية العمل معنا
                    @else
                        Comprehensive answers to the most common questions about our marketing services and work process
                    @endif
                </p>
                <div class="search-box" data-aos="fade-up" data-aos-delay="200">
                    <div class="input-group input-group-lg">
                        <span class="input-group-text bg-white border-end-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control border-start-0" id="faqSearch" 
                               placeholder="{{ session('locale', 'ar') === 'ar' ? 'ابحث في الأسئلة الشائعة...' : 'Search FAQ...' }}">
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Categories -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="faq-categories text-center mb-5" data-aos="fade-up">
                    <button class="category-btn active" data-category="all">
                        <i class="fas fa-th-large me-2"></i>
                        {{ session('locale', 'ar') === 'ar' ? 'جميع الأسئلة' : 'All Questions' }}
                    </button>
                    @foreach($categories as $key => $category)
                    <button class="category-btn" data-category="{{ $key }}">
                        <i class="fas fa-{{ $key === 'general' ? 'info-circle' : ($key === 'services' ? 'cogs' : ($key === 'pricing' ? 'dollar-sign' : ($key === 'process' ? 'tasks' : 'headset'))) }} me-2"></i>
                        {{ $category }}
                    </button>
                    @endforeach
                </div>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="faq-accordion" data-aos="fade-up">
                    @foreach($faqs as $faq)
                    <div class="faq-item" data-category="{{ $faq['category'] }}">
                        <div class="faq-question" onclick="toggleFaq(this)">
                            <div class="question-content">
                                <div class="question-icon">
                                    <i class="fas fa-question-circle"></i>
                                </div>
                                <div class="question-text">
                                    <h5>{{ $faq['question'] }}</h5>
                                    <span class="category-tag">{{ $categories[$faq['category']] }}</span>
                                </div>
                            </div>
                            <div class="toggle-icon">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <div class="answer-content">
                                <div class="answer-icon">
                                    <i class="fas fa-lightbulb text-warning"></i>
                                </div>
                                <div class="answer-text">
                                    <p>{{ $faq['answer'] }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                
                <!-- No Results Message -->
                <div id="noResults" class="text-center py-5" style="display: none;">
                    <i class="fas fa-search text-muted mb-3" style="font-size: 3rem;"></i>
                    <h4 class="text-muted">
                        {{ session('locale', 'ar') === 'ar' ? 'لم نجد أي نتائج' : 'No results found' }}
                    </h4>
                    <p class="text-muted">
                        {{ session('locale', 'ar') === 'ar' ? 'جرب البحث بكلمات مختلفة أو تصفح الفئات المختلفة' : 'Try searching with different keywords or browse different categories' }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Quick Help Section -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">
                    @if(session('locale', 'ar') === 'ar')
                        هل تحتاج مساعدة إضافية؟
                    @else
                        Need Additional Help?
                    @endif
                </h2>
                <p class="text-muted">
                    @if(session('locale', 'ar') === 'ar')
                        إذا لم تجد إجابة لسؤالك، فريقنا جاهز لمساعدتك
                    @else
                        If you didn't find an answer to your question, our team is ready to help
                    @endif
                </p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up">
                <div class="help-card">
                    <div class="help-icon bg-primary">
                        <i class="fas fa-phone text-white"></i>
                    </div>
                    <div class="help-content">
                        <h5>{{ session('locale', 'ar') === 'ar' ? 'اتصل بنا' : 'Call Us' }}</h5>
                        <p class="text-muted">{{ session('locale', 'ar') === 'ar' ? 'تحدث مع أحد خبرائنا مباشرة' : 'Speak directly with one of our experts' }}</p>
                        <a href="tel:+966123456789" class="btn btn-outline-primary">
                            <i class="fas fa-phone me-2"></i>
                            {{ session('locale', 'ar') === 'ar' ? 'اتصل الآن' : 'Call Now' }}
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="help-card">
                    <div class="help-icon bg-success">
                        <i class="fab fa-whatsapp text-white"></i>
                    </div>
                    <div class="help-content">
                        <h5>{{ session('locale', 'ar') === 'ar' ? 'واتساب' : 'WhatsApp' }}</h5>
                        <p class="text-muted">{{ session('locale', 'ar') === 'ar' ? 'راسلنا عبر الواتساب للرد السريع' : 'Message us on WhatsApp for quick response' }}</p>
                        <a href="https://wa.me/966123456789" class="btn btn-outline-success" target="_blank">
                            <i class="fab fa-whatsapp me-2"></i>
                            {{ session('locale', 'ar') === 'ar' ? 'راسلنا' : 'Message Us' }}
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="help-card">
                    <div class="help-icon bg-info">
                        <i class="fas fa-envelope text-white"></i>
                    </div>
                    <div class="help-content">
                        <h5>{{ session('locale', 'ar') === 'ar' ? 'البريد الإلكتروني' : 'Email' }}</h5>
                        <p class="text-muted">{{ session('locale', 'ar') === 'ar' ? 'أرسل لنا استفسارك مفصلاً' : 'Send us your detailed inquiry' }}</p>
                        <a href="mailto:<EMAIL>" class="btn btn-outline-info">
                            <i class="fas fa-envelope me-2"></i>
                            {{ session('locale', 'ar') === 'ar' ? 'راسلنا' : 'Email Us' }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section-padding bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <h2 class="mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        مستعد لبدء مشروعك؟
                    @else
                        Ready to Start Your Project?
                    @endif
                </h2>
                <p class="lead mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        احصل على استشارة مجانية واكتشف كيف يمكننا مساعدتك في تحقيق أهدافك التسويقية
                    @else
                        Get a free consultation and discover how we can help you achieve your marketing goals
                    @endif
                </p>
                <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                    <a href="{{ route('quote') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-rocket me-2"></i>
                        {{ session('locale', 'ar') === 'ar' ? 'احصل على عرض سعر' : 'Get Quote' }}
                    </a>
                    <a href="{{ route('contact') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-calendar me-2"></i>
                        {{ session('locale', 'ar') === 'ar' ? 'احجز استشارة مجانية' : 'Book Free Consultation' }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.search-box {
    max-width: 600px;
    margin: 0 auto;
}

.search-box .input-group-text {
    border-radius: 25px 0 0 25px;
}

.search-box .form-control {
    border-radius: 0 25px 25px 0;
    border-left: none;
}

.search-box .form-control:focus {
    box-shadow: none;
    border-color: #007bff;
}

.faq-categories {
    margin-bottom: 3rem;
}

.category-btn {
    background: white;
    border: 2px solid #e9ecef;
    color: #6c757d;
    padding: 0.75rem 1.5rem;
    margin: 0.25rem;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.category-btn:hover,
.category-btn.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
    transform: translateY(-2px);
}

.faq-item {
    background: white;
    border-radius: 15px;
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    box-shadow: 0 10px 25px rgba(0,0,0,0.12);
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background-color 0.3s ease;
}

.faq-question:hover {
    background-color: #f8f9fa;
}

.question-content {
    display: flex;
    align-items: center;
    flex-grow: 1;
}

.question-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.question-icon i {
    color: white;
    font-size: 1.2rem;
}

.question-text h5 {
    margin: 0 0 0.5rem;
    font-weight: 600;
    color: #212529;
}

.category-tag {
    background: #e9ecef;
    color: #6c757d;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.toggle-icon {
    transition: transform 0.3s ease;
}

.faq-item.active .toggle-icon {
    transform: rotate(180deg);
}

.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: #f8f9fa;
}

.faq-item.active .faq-answer {
    max-height: 300px;
}

.answer-content {
    padding: 1.5rem;
    display: flex;
    align-items: flex-start;
}

.answer-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.answer-text p {
    margin: 0;
    line-height: 1.6;
    color: #6c757d;
}

.help-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.help-card:hover {
    transform: translateY(-10px);
}

.help-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
}

.help-content h5 {
    font-weight: bold;
    margin-bottom: 1rem;
}

.faq-item.hidden {
    display: none;
}

@media (max-width: 768px) {
    .question-content {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .question-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }
    
    .category-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        margin: 0.25rem 0.125rem;
    }
    
    .help-card {
        margin-bottom: 2rem;
    }
}
</style>

<script>
// FAQ functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('faqSearch');
    const categoryBtns = document.querySelectorAll('.category-btn');
    const faqItems = document.querySelectorAll('.faq-item');
    const noResults = document.getElementById('noResults');
    
    // Search functionality
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        filterFAQs(searchTerm, getActiveCategory());
    });
    
    // Category filtering
    categoryBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            categoryBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            const category = this.getAttribute('data-category');
            const searchTerm = searchInput.value.toLowerCase();
            filterFAQs(searchTerm, category);
        });
    });
    
    function getActiveCategory() {
        const activeBtn = document.querySelector('.category-btn.active');
        return activeBtn ? activeBtn.getAttribute('data-category') : 'all';
    }
    
    function filterFAQs(searchTerm, category) {
        let visibleCount = 0;
        
        faqItems.forEach(item => {
            const itemCategory = item.getAttribute('data-category');
            const questionText = item.querySelector('.question-text h5').textContent.toLowerCase();
            const answerText = item.querySelector('.answer-text p').textContent.toLowerCase();
            
            const matchesSearch = searchTerm === '' || 
                                questionText.includes(searchTerm) || 
                                answerText.includes(searchTerm);
            const matchesCategory = category === 'all' || itemCategory === category;
            
            if (matchesSearch && matchesCategory) {
                item.classList.remove('hidden');
                visibleCount++;
            } else {
                item.classList.add('hidden');
            }
        });
        
        // Show/hide no results message
        if (visibleCount === 0) {
            noResults.style.display = 'block';
        } else {
            noResults.style.display = 'none';
        }
    }
});

// Toggle FAQ function
function toggleFaq(element) {
    const faqItem = element.closest('.faq-item');
    const isActive = faqItem.classList.contains('active');
    
    // Close all other FAQ items
    document.querySelectorAll('.faq-item.active').forEach(item => {
        if (item !== faqItem) {
            item.classList.remove('active');
        }
    });
    
    // Toggle current item
    if (isActive) {
        faqItem.classList.remove('active');
    } else {
        faqItem.classList.add('active');
    }
}
</script>
@endsection
