@extends('layouts.app')

@section('title', 'Pricing & Packages - Agent Marketing | Digital Marketing Service Plans')
@section('description', 'Choose the perfect digital marketing package for your business. Competitive pricing for social media management, SEO, advertising campaigns, and comprehensive marketing strategies.')
@section('keywords', 'digital marketing pricing, marketing packages, social media management cost, SEO pricing, advertising packages, marketing agency rates, digital marketing plans')
@section('og_title', 'Agent Marketing Pricing - Affordable Digital Marketing Packages')
@section('og_description', 'Discover our competitive pricing for digital marketing services. From starter packages to enterprise solutions, find the perfect plan for your business growth.')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center" style="min-height: 60vh;">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <h1 class="display-4 fw-bold mb-4">
                    Pricing & Packages
                </h1>
                <p class="lead mb-4">
                    Choose the package that fits your needs and budget from a variety of specialized marketing services
                </p>
                <div class="pricing-toggle" data-aos="fade-up" data-aos-delay="200">
                    <div class="d-flex align-items-center justify-content-center">
                        <span class="me-3">Monthly</span>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="pricingToggle">
                        </div>
                        <span class="ms-3">
                            Annual
                            <span class="badge bg-success ms-2">Save 20%</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Pricing Packages -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            @foreach($packages as $package)
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                <div class="pricing-card {{ $package['popular'] ? 'popular' : '' }}">
                    @if($package['popular'])
                    <div class="popular-badge">
                        <i class="fas fa-star me-1"></i>
                        {{ session('locale', 'ar') === 'ar' ? 'الأكثر طلباً' : 'Most Popular' }}
                    </div>
                    @endif
                    
                    <div class="pricing-header text-center">
                        <div class="pricing-icon bg-{{ $package['color'] }} mb-3">
                            <i class="{{ $package['icon'] }} text-white"></i>
                        </div>
                        <h3 class="package-name">{{ $package['name'] }}</h3>
                        <p class="package-description">{{ $package['description'] }}</p>
                        <div class="pricing-amount">
                            <span class="currency">{{ session('locale', 'ar') === 'ar' ? 'ر.س' : 'SAR' }}</span>
                            <span class="price monthly-price">{{ $package['price'] }}</span>
                            <span class="price annual-price" style="display: none;">{{ number_format(floatval(str_replace(',', '', $package['price'])) * 12 * 0.8) }}</span>
                            <span class="duration">
                                <span class="monthly-duration">/ {{ $package['duration'] }}</span>
                                <span class="annual-duration" style="display: none;">/ {{ session('locale', 'ar') === 'ar' ? 'سنوياً' : 'Annually' }}</span>
                            </span>
                        </div>
                    </div>
                    
                    <div class="pricing-features">
                        <ul class="features-list">
                            @foreach($package['features'] as $feature)
                            <li>
                                <i class="fas fa-check text-{{ $package['color'] }} me-2"></i>
                                {{ $feature }}
                            </li>
                            @endforeach
                        </ul>
                    </div>
                    
                    <div class="pricing-footer text-center">
                        <a href="{{ route('quote') }}?package={{ $package['id'] }}" class="btn btn-{{ $package['color'] }} btn-lg w-100">
                            <i class="fas fa-rocket me-2"></i>
                            {{ session('locale', 'ar') === 'ar' ? 'ابدأ الآن' : 'Get Started' }}
                        </a>
                        <p class="mt-3 text-muted small">
                            {{ session('locale', 'ar') === 'ar' ? 'بدون رسوم إعداد • إلغاء في أي وقت' : 'No setup fees • Cancel anytime' }}
                        </p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Add-on Services -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">
                    @if(session('locale', 'ar') === 'ar')
                        خدمات إضافية
                    @else
                        Add-on Services
                    @endif
                </h2>
                <p class="text-muted">
                    @if(session('locale', 'ar') === 'ar')
                        عزز باقتك بخدمات إضافية متخصصة لتحقيق أفضل النتائج
                    @else
                        Enhance your package with specialized add-on services for best results
                    @endif
                </p>
            </div>
        </div>
        
        <div class="row">
            @foreach($addons as $addon)
            <div class="col-lg-6 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                <div class="addon-card">
                    <div class="addon-icon">
                        <i class="{{ $addon['icon'] }}"></i>
                    </div>
                    <div class="addon-content">
                        <h5 class="addon-name">{{ $addon['name'] }}</h5>
                        <p class="addon-description">{{ $addon['description'] }}</p>
                        <div class="addon-price">
                            <span class="price">{{ session('locale', 'ar') === 'ar' ? 'ر.س' : 'SAR' }} {{ $addon['price'] }}</span>
                            <span class="duration">{{ session('locale', 'ar') === 'ar' ? 'لمرة واحدة' : 'One-time' }}</span>
                        </div>
                    </div>
                    <div class="addon-action">
                        <a href="{{ route('quote') }}?addon={{ Str::slug($addon['name']) }}" class="btn btn-outline-primary">
                            <i class="fas fa-plus me-2"></i>
                            {{ session('locale', 'ar') === 'ar' ? 'أضف للباقة' : 'Add to Package' }}
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Comparison Table -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">
                    @if(session('locale', 'ar') === 'ar')
                        مقارنة الباقات
                    @else
                        Package Comparison
                    @endif
                </h2>
                <p class="text-muted">
                    @if(session('locale', 'ar') === 'ar')
                        قارن بين الباقات المختلفة لاختيار الأنسب لك
                    @else
                        Compare different packages to choose the best fit for you
                    @endif
                </p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-12">
                <div class="comparison-table-wrapper" data-aos="fade-up">
                    <table class="table comparison-table">
                        <thead>
                            <tr>
                                <th>{{ session('locale', 'ar') === 'ar' ? 'الميزات' : 'Features' }}</th>
                                @foreach($packages as $package)
                                <th class="text-center">{{ $package['name'] }}</th>
                                @endforeach
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>{{ session('locale', 'ar') === 'ar' ? 'السعر الشهري' : 'Monthly Price' }}</strong></td>
                                @foreach($packages as $package)
                                <td class="text-center">
                                    <span class="price-highlight">{{ session('locale', 'ar') === 'ar' ? 'ر.س' : 'SAR' }} {{ $package['price'] }}</span>
                                </td>
                                @endforeach
                            </tr>
                            <tr>
                                <td>{{ session('locale', 'ar') === 'ar' ? 'حسابات وسائل التواصل' : 'Social Media Accounts' }}</td>
                                <td class="text-center">1</td>
                                <td class="text-center">3</td>
                                <td class="text-center">{{ session('locale', 'ar') === 'ar' ? 'غير محدود' : 'Unlimited' }}</td>
                            </tr>
                            <tr>
                                <td>{{ session('locale', 'ar') === 'ar' ? 'المنشورات الشهرية' : 'Monthly Posts' }}</td>
                                <td class="text-center">10</td>
                                <td class="text-center">20</td>
                                <td class="text-center">40</td>
                            </tr>
                            <tr>
                                <td>{{ session('locale', 'ar') === 'ar' ? 'الحملات الإعلانية' : 'Ad Campaigns' }}</td>
                                <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                <td class="text-center">1</td>
                                <td class="text-center">3</td>
                            </tr>
                            <tr>
                                <td>{{ session('locale', 'ar') === 'ar' ? 'مدير حساب مخصص' : 'Dedicated Account Manager' }}</td>
                                <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                <td class="text-center"><i class="fas fa-check text-success"></i></td>
                            </tr>
                            <tr>
                                <td>{{ session('locale', 'ar') === 'ar' ? 'الدعم الفني' : 'Technical Support' }}</td>
                                <td class="text-center">{{ session('locale', 'ar') === 'ar' ? 'أساسي' : 'Basic' }}</td>
                                <td class="text-center">{{ session('locale', 'ar') === 'ar' ? 'متقدم' : 'Advanced' }}</td>
                                <td class="text-center">24/7</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">
                    @if(session('locale', 'ar') === 'ar')
                        الأسئلة الشائعة
                    @else
                        Frequently Asked Questions
                    @endif
                </h2>
                <p class="text-muted">
                    @if(session('locale', 'ar') === 'ar')
                        إجابات على أكثر الأسئلة شيوعاً حول باقاتنا وخدماتنا
                    @else
                        Answers to the most common questions about our packages and services
                    @endif
                </p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="accordion" id="pricingFAQ" data-aos="fade-up">
                    @foreach($faqs as $faq)
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq{{ $loop->index }}">
                            <button class="accordion-button {{ $loop->first ? '' : 'collapsed' }}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ $loop->index }}">
                                {{ $faq['question'] }}
                            </button>
                        </h2>
                        <div id="collapse{{ $loop->index }}" class="accordion-collapse collapse {{ $loop->first ? 'show' : '' }}" data-bs-parent="#pricingFAQ">
                            <div class="accordion-body">
                                {{ $faq['answer'] }}
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section-padding bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <h2 class="mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        هل تحتاج باقة مخصصة؟
                    @else
                        Need a Custom Package?
                    @endif
                </h2>
                <p class="lead mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        تواصل معنا لتصميم باقة تناسب احتياجاتك الخاصة وميزانيتك
                    @else
                        Contact us to design a package that fits your specific needs and budget
                    @endif
                </p>
                <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                    <a href="{{ route('quote') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-paper-plane me-2"></i>
                        {{ session('locale', 'ar') === 'ar' ? 'احصل على عرض سعر' : 'Get Custom Quote' }}
                    </a>
                    <a href="{{ route('contact') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-phone me-2"></i>
                        {{ session('locale', 'ar') === 'ar' ? 'تحدث معنا' : 'Talk to Us' }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.pricing-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.pricing-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.pricing-card.popular {
    border: 3px solid #28a745;
    transform: scale(1.05);
}

.popular-badge {
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: #28a745;
    color: white;
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: bold;
}

.pricing-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 2rem;
}

.package-name {
    font-size: 1.5rem;
    font-weight: bold;
    margin: 1rem 0 0.5rem;
}

.package-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

.pricing-amount {
    margin-bottom: 2rem;
}

.currency {
    font-size: 1.2rem;
    color: #6c757d;
}

.price {
    font-size: 3rem;
    font-weight: bold;
    color: #212529;
}

.duration {
    color: #6c757d;
    font-size: 1rem;
}

.pricing-features {
    flex-grow: 1;
    margin-bottom: 2rem;
}

.features-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features-list li {
    padding: 0.75rem 0;
    border-bottom: 1px solid #f8f9fa;
    font-size: 0.95rem;
}

.features-list li:last-child {
    border-bottom: none;
}

.addon-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    height: 100%;
}

.addon-card:hover {
    transform: translateY(-5px);
}

.addon-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    flex-shrink: 0;
}

.addon-content {
    flex-grow: 1;
}

.addon-name {
    font-size: 1.1rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.addon-description {
    color: #6c757d;
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
}

.addon-price .price {
    font-size: 1.2rem;
    font-weight: bold;
    color: #007bff;
}

.addon-price .duration {
    color: #6c757d;
    font-size: 0.85rem;
    margin-left: 0.5rem;
}

.comparison-table-wrapper {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.comparison-table {
    margin: 0;
}

.comparison-table th {
    background: #f8f9fa;
    border: none;
    padding: 1.5rem 1rem;
    font-weight: bold;
    text-align: center;
}

.comparison-table td {
    border: none;
    padding: 1rem;
    vertical-align: middle;
}

.comparison-table tbody tr:nth-child(even) {
    background: #f8f9fa;
}

.price-highlight {
    color: #007bff;
    font-weight: bold;
    font-size: 1.1rem;
}

.pricing-toggle {
    margin-bottom: 2rem;
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

@media (max-width: 768px) {
    .pricing-card {
        margin-bottom: 2rem;
    }
    
    .pricing-card.popular {
        transform: none;
    }
    
    .price {
        font-size: 2.5rem;
    }
    
    .addon-card {
        flex-direction: column;
        text-align: center;
    }
    
    .comparison-table-wrapper {
        overflow-x: auto;
    }
    
    .comparison-table {
        min-width: 600px;
    }
}
</style>

<script>
// Pricing toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const pricingToggle = document.getElementById('pricingToggle');
    const monthlyPrices = document.querySelectorAll('.monthly-price');
    const annualPrices = document.querySelectorAll('.annual-price');
    const monthlyDurations = document.querySelectorAll('.monthly-duration');
    const annualDurations = document.querySelectorAll('.annual-duration');
    
    pricingToggle.addEventListener('change', function() {
        if (this.checked) {
            // Show annual pricing
            monthlyPrices.forEach(price => price.style.display = 'none');
            annualPrices.forEach(price => price.style.display = 'inline');
            monthlyDurations.forEach(duration => duration.style.display = 'none');
            annualDurations.forEach(duration => duration.style.display = 'inline');
        } else {
            // Show monthly pricing
            monthlyPrices.forEach(price => price.style.display = 'inline');
            annualPrices.forEach(price => price.style.display = 'none');
            monthlyDurations.forEach(duration => duration.style.display = 'inline');
            annualDurations.forEach(duration => duration.style.display = 'none');
        }
    });
});
</script>
@endsection
