@extends('admin.layouts.app')

@section('title', 'عرض رسالة التواصل')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">عرض رسالة التواصل</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.contacts.index') }}">رسائل التواصل</a></li>
                    <li class="breadcrumb-item active">عرض الرسالة</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.contacts.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Contact Message Details -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">تفاصيل الرسالة</h6>
                    <div>
                        @switch($contact['status'])
                            @case('new')
                                <span class="badge bg-warning">جديدة</span>
                                @break
                            @case('read')
                                <span class="badge bg-info">مقروءة</span>
                                @break
                            @case('replied')
                                <span class="badge bg-success">تم الرد</span>
                                @break
                            @default
                                <span class="badge bg-secondary">غير محدد</span>
                        @endswitch
                    </div>
                </div>
                <div class="card-body">
                    <!-- Contact Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted">الاسم</h6>
                            <p class="h5">{{ $contact['name'] }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">البريد الإلكتروني</h6>
                            <p class="h5">
                                <a href="mailto:{{ $contact['email'] }}" class="text-decoration-none">
                                    {{ $contact['email'] }}
                                </a>
                            </p>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted">رقم الهاتف</h6>
                            <p class="h5">
                                @if(isset($contact['phone']) && $contact['phone'])
                                    <a href="tel:{{ $contact['phone'] }}" class="text-decoration-none">
                                        {{ $contact['phone'] }}
                                    </a>
                                @else
                                    <span class="text-muted">غير محدد</span>
                                @endif
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">تاريخ الإرسال</h6>
                            <p class="h5">{{ date('Y/m/d H:i', strtotime($contact['created_at'])) }}</p>
                        </div>
                    </div>

                    <!-- Subject -->
                    <div class="mb-4">
                        <h6 class="text-muted">الموضوع</h6>
                        <p class="h5">{{ $contact['subject'] }}</p>
                    </div>

                    <!-- Message Content -->
                    <div class="mb-4">
                        <h6 class="text-muted">محتوى الرسالة</h6>
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0" style="white-space: pre-wrap;">{{ $contact['message'] }}</p>
                        </div>
                    </div>

                    <!-- Additional Info -->
                    @if(isset($contact['company']) && $contact['company'])
                    <div class="mb-4">
                        <h6 class="text-muted">الشركة</h6>
                        <p>{{ $contact['company'] }}</p>
                    </div>
                    @endif

                    @if(isset($contact['service_interest']) && $contact['service_interest'])
                    <div class="mb-4">
                        <h6 class="text-muted">الخدمة المهتم بها</h6>
                        <p>{{ $contact['service_interest'] }}</p>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Reply Section -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">الرد على الرسالة</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.contacts.reply', $contact['id']) }}" method="POST">
                        @csrf
                        <div class="mb-3">
                            <label for="reply_subject" class="form-label">موضوع الرد</label>
                            <input type="text" class="form-control" id="reply_subject" name="reply_subject" 
                                   value="Re: {{ $contact['subject'] }}" required>
                        </div>
                        <div class="mb-3">
                            <label for="reply_message" class="form-label">محتوى الرد</label>
                            <textarea class="form-control" id="reply_message" name="reply_message" 
                                      rows="6" required placeholder="اكتب ردك هنا..."></textarea>
                        </div>
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>
                                إرسال الرد
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="openEmailClient()">
                                <i class="fas fa-external-link-alt me-2"></i>
                                فتح في برنامج البريد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if($contact['status'] == 'new')
                        <button onclick="markAsRead({{ $contact['id'] }})" class="btn btn-success">
                            <i class="fas fa-check me-2"></i>
                            تحديد كمقروء
                        </button>
                        @endif
                        
                        <button onclick="updateStatus('replied')" class="btn btn-info">
                            <i class="fas fa-reply me-2"></i>
                            تحديد كـ "تم الرد"
                        </button>
                        
                        <a href="mailto:{{ $contact['email'] }}" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-2"></i>
                            إرسال بريد إلكتروني
                        </a>
                        
                        @if(isset($contact['phone']) && $contact['phone'])
                        <a href="tel:{{ $contact['phone'] }}" class="btn btn-outline-success">
                            <i class="fas fa-phone me-2"></i>
                            اتصال هاتفي
                        </a>
                        @endif
                        
                        <button onclick="deleteContact({{ $contact['id'] }})" class="btn btn-outline-danger">
                            <i class="fas fa-trash me-2"></i>
                            حذف الرسالة
                        </button>
                    </div>
                </div>
            </div>

            <!-- Contact History -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">سجل التواصل</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم استلام الرسالة</h6>
                                <small class="text-muted">{{ date('Y/m/d H:i', strtotime($contact['created_at'])) }}</small>
                            </div>
                        </div>
                        
                        @if($contact['status'] != 'new')
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم قراءة الرسالة</h6>
                                <small class="text-muted">{{ date('Y/m/d H:i') }}</small>
                            </div>
                        </div>
                        @endif
                        
                        @if($contact['status'] == 'replied')
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم الرد على الرسالة</h6>
                                <small class="text-muted">{{ date('Y/m/d H:i') }}</small>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Contact Stats -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">إحصائيات العميل</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary mb-1">1</h4>
                                <small class="text-muted">رسائل سابقة</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success mb-1">0</h4>
                            <small class="text-muted">مشاريع منجزة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذه الرسالة؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">حذف</button>
            </div>
        </div>
    </div>
</div>

@endsection

@section('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 20px;
    width: 2px;
    height: calc(100% + 10px);
    background-color: #e3e6f0;
}

.timeline-marker {
    position: absolute;
    left: -26px;
    top: 4px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e3e6f0;
}

.timeline-content {
    padding-left: 10px;
}
</style>
@endsection

@section('scripts')
<script>
function markAsRead(contactId) {
    fetch(`/admin/contacts/${contactId}/mark-read`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ أثناء تحديث الحالة');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تحديث الحالة');
    });
}

function updateStatus(status) {
    fetch(`/admin/contacts/{{ $contact['id'] }}/status`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: status })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ أثناء تحديث الحالة');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تحديث الحالة');
    });
}

function deleteContact(contactId) {
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function openEmailClient() {
    const subject = encodeURIComponent('Re: {{ $contact['subject'] }}');
    const body = encodeURIComponent(`مرحباً {{ $contact['name'] }},\n\nشكراً لتواصلك معنا.\n\n`);
    const mailtoLink = `mailto:{{ $contact['email'] }}?subject=${subject}&body=${body}`;
    window.open(mailtoLink);
}

document.getElementById('confirmDelete').addEventListener('click', function() {
    fetch(`/admin/contacts/{{ $contact['id'] }}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = '{{ route('admin.contacts.index') }}';
        } else {
            alert('حدث خطأ أثناء حذف الرسالة');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء حذف الرسالة');
    });
    
    bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
});
</script>
@endsection
