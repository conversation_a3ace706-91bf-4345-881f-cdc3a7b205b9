@extends('admin.layouts.app')

@section('title', 'إدارة طلبات العروض')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">إدارة طلبات العروض</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item active">طلبات العروض</li>
                </ol>
            </nav>
        </div>
        <div>
            <button class="btn btn-outline-primary" onclick="exportQuotes()">
                <i class="fas fa-download me-2"></i>
                تصدير البيانات
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي الطلبات
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ count($quotes) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-file-invoice fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                قيد المراجعة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ collect($quotes)->where('status', 'pending')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                تم الإرسال
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ collect($quotes)->where('status', 'sent')->count() }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                القيمة المتوقعة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format(collect($quotes)->sum(function($quote) { 
                                    return (int)str_replace(['SAR', ' ', ','], '', explode('-', $quote['budget'])[0] ?? '0'); 
                                })) }} ر.س
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">فلترة الطلبات</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ route('admin.quotes.index') }}">
                <div class="row">
                    <div class="col-md-3">
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>قيد المراجعة</option>
                            <option value="sent" {{ request('status') == 'sent' ? 'selected' : '' }}>تم الإرسال</option>
                            <option value="accepted" {{ request('status') == 'accepted' ? 'selected' : '' }}>مقبول</option>
                            <option value="rejected" {{ request('status') == 'rejected' ? 'selected' : '' }}>مرفوض</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select name="service" class="form-select">
                            <option value="">جميع الخدمات</option>
                            <option value="social-media" {{ request('service') == 'social-media' ? 'selected' : '' }}>إدارة وسائل التواصل</option>
                            <option value="seo" {{ request('service') == 'seo' ? 'selected' : '' }}>تحسين محركات البحث</option>
                            <option value="ads" {{ request('service') == 'ads' ? 'selected' : '' }}>الإعلانات المدفوعة</option>
                            <option value="website" {{ request('service') == 'website' ? 'selected' : '' }}>تصميم المواقع</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <input type="date" name="date_from" class="form-control" placeholder="من تاريخ" value="{{ request('date_from') }}">
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>بحث
                        </button>
                        <a href="{{ route('admin.quotes.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Quotes Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">قائمة طلبات العروض</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>العميل</th>
                            <th>الخدمة المطلوبة</th>
                            <th>الميزانية</th>
                            <th>الحالة</th>
                            <th>تاريخ الطلب</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($quotes as $quote)
                        <tr class="{{ $quote['status'] == 'pending' ? 'table-warning' : '' }}">
                            <td>
                                <div class="d-flex align-items-center">
                                    @if($quote['status'] == 'pending')
                                        <span class="badge bg-warning me-2">جديد</span>
                                    @endif
                                    <div>
                                        <strong>{{ $quote['name'] }}</strong>
                                        <br>
                                        <small class="text-muted">{{ $quote['email'] }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $quote['service'] }}</span>
                                @if(isset($quote['company']) && $quote['company'])
                                    <br><small class="text-muted">{{ $quote['company'] }}</small>
                                @endif
                            </td>
                            <td>
                                <strong>{{ $quote['budget'] }}</strong>
                            </td>
                            <td>
                                @switch($quote['status'])
                                    @case('pending')
                                        <span class="badge bg-warning">قيد المراجعة</span>
                                        @break
                                    @case('sent')
                                        <span class="badge bg-info">تم الإرسال</span>
                                        @break
                                    @case('accepted')
                                        <span class="badge bg-success">مقبول</span>
                                        @break
                                    @case('rejected')
                                        <span class="badge bg-danger">مرفوض</span>
                                        @break
                                    @default
                                        <span class="badge bg-secondary">غير محدد</span>
                                @endswitch
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ date('Y/m/d H:i', strtotime($quote['created_at'])) }}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.quotes.show', $quote['id']) }}" 
                                       class="btn btn-sm btn-outline-primary" title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if($quote['status'] == 'pending')
                                    <button onclick="updateStatus({{ $quote['id'] }}, 'sent')" 
                                            class="btn btn-sm btn-outline-success" title="تحديد كـ مرسل">
                                        <i class="fas fa-paper-plane"></i>
                                    </button>
                                    @endif
                                    <button onclick="deleteQuote({{ $quote['id'] }})" 
                                            class="btn btn-sm btn-outline-danger" title="حذف">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="text-center py-4">
                                <div class="text-muted">
                                    <i class="fas fa-file-invoice fa-3x mb-3"></i>
                                    <p>لا توجد طلبات عروض حتى الآن</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف طلب العرض هذا؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">حذف</button>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
let quoteToDelete = null;

function updateStatus(quoteId, status) {
    fetch(`/admin/quotes/${quoteId}/status`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: status })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ أثناء تحديث الحالة');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تحديث الحالة');
    });
}

function deleteQuote(quoteId) {
    quoteToDelete = quoteId;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function exportQuotes() {
    window.location.href = '/admin/quotes/export';
}

document.getElementById('confirmDelete').addEventListener('click', function() {
    if (quoteToDelete) {
        fetch(`/admin/quotes/${quoteToDelete}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('حدث خطأ أثناء حذف الطلب');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('حدث خطأ أثناء حذف الطلب');
        });
        
        bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
        quoteToDelete = null;
    }
});
</script>
@endsection
