<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class TestimonialsController extends Controller
{
    public function index()
    {
        // Mock testimonials data - English only
        $testimonials = [
            [
                'id' => 1,
                'name' => '<PERSON>',
                'position' => 'Marketing Director',
                'company' => 'TechStart Inc.',
                'image' => '/images/testimonials/sarah-johnson.jpg',
                'rating' => 5,
                'content' => 'Agent Marketing transformed our digital presence completely. Their strategic approach to social media marketing increased our engagement by 300% in just 3 months. The team is professional, creative, and delivers exceptional results.',
                'project_type' => 'Social Media Marketing',
                'results' => [
                    'engagement' => '+300%',
                    'followers' => '+150%',
                    'leads' => '+200%'
                ],
                'date' => '2024-01-15',
                'featured' => true
            ],
            [
                'id' => 2,
                'name' => '<PERSON>',
                'position' => 'CEO',
                'company' => 'GrowthCorp',
                'image' => '/images/testimonials/michael-chen.jpg',
                'rating' => 5,
                'content' => 'Outstanding SEO services! Our website traffic increased by 250% within 6 months. Agent Marketing\'s data-driven approach and transparent reporting made all the difference. Highly recommended!',
                'project_type' => 'SEO Optimization',
                'results' => [
                    'traffic' => '+250%',
                    'keywords' => '+180%',
                    'conversions' => '+120%'
                ],
                'date' => '2024-02-20',
                'featured' => true
            ],
            [
                'id' => 3,
                'name' => 'Emily Rodriguez',
                'position' => 'Founder',
                'company' => 'Boutique Fashion',
                'image' => '/images/testimonials/emily-rodriguez.jpg',
                'rating' => 5,
                'content' => 'The PPC campaign management was exceptional. Our ROI improved by 400% and cost per acquisition dropped by 60%. The team\'s expertise in Google Ads is unmatched.',
                'project_type' => 'PPC Advertising',
                'results' => [
                    'roi' => '+400%',
                    'cpa' => '-60%',
                    'sales' => '+280%'
                ],
                'date' => '2024-03-10',
                'featured' => false
            ],
            [
                'id' => 4,
                'name' => 'David Thompson',
                'position' => 'Marketing Manager',
                'company' => 'HealthTech Solutions',
                'image' => '/images/testimonials/david-thompson.jpg',
                'rating' => 5,
                'content' => 'Comprehensive digital marketing strategy that delivered real results. Our brand awareness increased significantly, and we saw a 200% boost in qualified leads. Professional team with excellent communication.',
                'project_type' => 'Digital Marketing Strategy',
                'results' => [
                    'brand_awareness' => '+180%',
                    'leads' => '+200%',
                    'revenue' => '+150%'
                ],
                'date' => '2024-04-05',
                'featured' => false
            ],
            [
                'id' => 5,
                'name' => 'Lisa Wang',
                'position' => 'E-commerce Director',
                'company' => 'Online Retail Pro',
                'image' => '/images/testimonials/lisa-wang.jpg',
                'rating' => 5,
                'content' => 'Their content marketing strategy was brilliant. Blog traffic increased by 350% and our email subscriber base grew by 400%. The quality of content and consistency was impressive.',
                'project_type' => 'Content Marketing',
                'results' => [
                    'blog_traffic' => '+350%',
                    'subscribers' => '+400%',
                    'engagement' => '+220%'
                ],
                'date' => '2024-05-12',
                'featured' => true
            ],
            [
                'id' => 6,
                'name' => 'Robert Martinez',
                'position' => 'Business Owner',
                'company' => 'Local Services Hub',
                'image' => '/images/testimonials/robert-martinez.jpg',
                'rating' => 5,
                'content' => 'Local SEO services were game-changing for our business. We now rank #1 for all our target keywords and local bookings increased by 300%. Excellent ROI and professional service.',
                'project_type' => 'Local SEO',
                'results' => [
                    'local_rankings' => '#1 Position',
                    'bookings' => '+300%',
                    'visibility' => '+250%'
                ],
                'date' => '2024-06-18',
                'featured' => false
            ]
        ];

        // Calculate overall statistics
        $stats = [
            'total_clients' => 150,
            'success_rate' => 98,
            'avg_roi_increase' => 280,
            'projects_completed' => 300,
            'avg_rating' => 4.9,
            'years_experience' => 8
        ];

        // Service categories for filtering
        $services = [
            'all' => 'All Services',
            'seo' => 'SEO Optimization',
            'ppc' => 'PPC Advertising',
            'social-media' => 'Social Media Marketing',
            'content' => 'Content Marketing',
            'strategy' => 'Digital Strategy',
            'local-seo' => 'Local SEO'
        ];

        return view('testimonials.index', compact('testimonials', 'stats', 'services'));
    }
}
