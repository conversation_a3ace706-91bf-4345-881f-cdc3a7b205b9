@extends('admin.layouts.app')

@section('title', 'إضافة مقال جديد')
@section('page-title', 'إضافة مقال جديد')

@section('content')
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ route('admin.blog.index') }}">إدارة المدونة</a></li>
                <li class="breadcrumb-item active">إضافة مقال جديد</li>
            </ol>
        </nav>
    </div>
    <a href="{{ route('admin.blog.index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>
        العودة للقائمة
    </a>
</div>

<form method="POST" action="{{ route('admin.blog.store') }}" enctype="multipart/form-data">
    @csrf
    
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card mb-4" data-aos="fade-up">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h5>
                </div>
                <div class="card-body">
                    <!-- Arabic Title -->
                    <div class="mb-3">
                        <label for="title_ar" class="form-label">
                            <i class="fas fa-heading me-1"></i>
                            العنوان (عربي) <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               class="form-control @error('title_ar') is-invalid @enderror" 
                               id="title_ar" 
                               name="title_ar" 
                               value="{{ old('title_ar') }}" 
                               required
                               placeholder="أدخل عنوان المقال باللغة العربية">
                        @error('title_ar')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- English Title -->
                    <div class="mb-3">
                        <label for="title_en" class="form-label">
                            <i class="fas fa-heading me-1"></i>
                            العنوان (إنجليزي) <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               class="form-control @error('title_en') is-invalid @enderror" 
                               id="title_en" 
                               name="title_en" 
                               value="{{ old('title_en') }}" 
                               required
                               placeholder="Enter article title in English">
                        @error('title_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Arabic Excerpt -->
                    <div class="mb-3">
                        <label for="excerpt_ar" class="form-label">
                            <i class="fas fa-align-left me-1"></i>
                            المقدمة (عربي) <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control @error('excerpt_ar') is-invalid @enderror" 
                                  id="excerpt_ar" 
                                  name="excerpt_ar" 
                                  rows="3" 
                                  required
                                  placeholder="أدخل مقدمة المقال باللغة العربية">{{ old('excerpt_ar') }}</textarea>
                        @error('excerpt_ar')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- English Excerpt -->
                    <div class="mb-3">
                        <label for="excerpt_en" class="form-label">
                            <i class="fas fa-align-left me-1"></i>
                            المقدمة (إنجليزي) <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control @error('excerpt_en') is-invalid @enderror" 
                                  id="excerpt_en" 
                                  name="excerpt_en" 
                                  rows="3" 
                                  required
                                  placeholder="Enter article excerpt in English">{{ old('excerpt_en') }}</textarea>
                        @error('excerpt_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Content -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="card-header">
                    <h5><i class="fas fa-file-alt me-2"></i>محتوى المقال</h5>
                </div>
                <div class="card-body">
                    <!-- Arabic Content -->
                    <div class="mb-3">
                        <label for="content_ar" class="form-label">
                            <i class="fas fa-paragraph me-1"></i>
                            المحتوى (عربي) <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control @error('content_ar') is-invalid @enderror" 
                                  id="content_ar" 
                                  name="content_ar" 
                                  rows="10" 
                                  required
                                  placeholder="أدخل محتوى المقال باللغة العربية">{{ old('content_ar') }}</textarea>
                        @error('content_ar')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- English Content -->
                    <div class="mb-3">
                        <label for="content_en" class="form-label">
                            <i class="fas fa-paragraph me-1"></i>
                            المحتوى (إنجليزي) <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control @error('content_en') is-invalid @enderror" 
                                  id="content_en" 
                                  name="content_en" 
                                  rows="10" 
                                  required
                                  placeholder="Enter article content in English">{{ old('content_en') }}</textarea>
                        @error('content_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Publish Settings -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="card-header">
                    <h5><i class="fas fa-cog me-2"></i>إعدادات النشر</h5>
                </div>
                <div class="card-body">
                    <!-- Status -->
                    <div class="mb-3">
                        <label for="status" class="form-label">
                            <i class="fas fa-toggle-on me-1"></i>
                            حالة المقال
                        </label>
                        <select class="form-select" id="status" name="status">
                            <option value="draft" {{ old('status') === 'draft' ? 'selected' : '' }}>مسودة</option>
                            <option value="published" {{ old('status') === 'published' ? 'selected' : '' }}>منشور</option>
                        </select>
                    </div>
                    
                    <!-- Featured -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="featured" 
                                   name="featured" 
                                   value="1"
                                   {{ old('featured') ? 'checked' : '' }}>
                            <label class="form-check-label" for="featured">
                                <i class="fas fa-star me-1"></i>
                                مقال مميز
                            </label>
                        </div>
                        <small class="text-muted">سيظهر المقال في قسم المقالات المميزة</small>
                    </div>
                    
                    <!-- Publish Date -->
                    <div class="mb-3">
                        <label for="published_at" class="form-label">
                            <i class="fas fa-calendar me-1"></i>
                            تاريخ النشر
                        </label>
                        <input type="datetime-local" 
                               class="form-control" 
                               id="published_at" 
                               name="published_at" 
                               value="{{ old('published_at', date('Y-m-d\TH:i')) }}">
                        <small class="text-muted">اتركه فارغاً للنشر الآن</small>
                    </div>
                </div>
            </div>
            
            <!-- Category and Tags -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="card-header">
                    <h5><i class="fas fa-tags me-2"></i>التصنيف والعلامات</h5>
                </div>
                <div class="card-body">
                    <!-- Category -->
                    <div class="mb-3">
                        <label for="category" class="form-label">
                            <i class="fas fa-folder me-1"></i>
                            الفئة <span class="text-danger">*</span>
                        </label>
                        <select class="form-select @error('category') is-invalid @enderror" 
                                id="category" 
                                name="category" 
                                required>
                            <option value="">اختر الفئة</option>
                            @foreach($categories as $key => $value)
                                <option value="{{ $key }}" {{ old('category') === $key ? 'selected' : '' }}>
                                    {{ $value }}
                                </option>
                            @endforeach
                        </select>
                        @error('category')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Tags -->
                    <div class="mb-3">
                        <label for="tags" class="form-label">
                            <i class="fas fa-hashtag me-1"></i>
                            العلامات <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               class="form-control @error('tags') is-invalid @enderror" 
                               id="tags" 
                               name="tags" 
                               value="{{ old('tags') }}" 
                               required
                               placeholder="مثال: تسويق، إعلانات، سوشيال ميديا">
                        <small class="text-muted">افصل بين العلامات بفاصلة</small>
                        @error('tags')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Featured Image -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="400">
                <div class="card-header">
                    <h5><i class="fas fa-image me-2"></i>الصورة المميزة</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <input type="file" 
                               class="form-control @error('image') is-invalid @enderror" 
                               id="image" 
                               name="image" 
                               accept="image/*"
                               onchange="previewImage(this)">
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">الحد الأقصى: 2MB، الصيغ المدعومة: JPG, PNG, GIF</small>
                    </div>
                    
                    <!-- Image Preview -->
                    <div id="imagePreview" class="text-center" style="display: none;">
                        <img id="previewImg" src="" alt="معاينة الصورة" class="img-fluid rounded" style="max-height: 200px;">
                        <button type="button" class="btn btn-sm btn-outline-danger mt-2" onclick="removeImage()">
                            <i class="fas fa-trash me-1"></i>
                            إزالة الصورة
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- SEO Settings -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="500">
                <div class="card-header">
                    <h5><i class="fas fa-search me-2"></i>إعدادات SEO</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="meta_description" class="form-label">
                            <i class="fas fa-file-text me-1"></i>
                            وصف الميتا
                        </label>
                        <textarea class="form-control" 
                                  id="meta_description" 
                                  name="meta_description" 
                                  rows="3" 
                                  maxlength="160"
                                  placeholder="وصف مختصر للمقال (160 حرف كحد أقصى)">{{ old('meta_description') }}</textarea>
                        <small class="text-muted">
                            <span id="metaCounter">0</span>/160 حرف
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="meta_keywords" class="form-label">
                            <i class="fas fa-key me-1"></i>
                            الكلمات المفتاحية
                        </label>
                        <input type="text" 
                               class="form-control" 
                               id="meta_keywords" 
                               name="meta_keywords" 
                               value="{{ old('meta_keywords') }}" 
                               placeholder="كلمات مفتاحية مفصولة بفاصلة">
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="row">
        <div class="col-12">
            <div class="card" data-aos="fade-up" data-aos-delay="600">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" name="action" value="save" class="btn btn-primary me-2">
                                <i class="fas fa-save me-2"></i>
                                حفظ المقال
                            </button>
                            <button type="submit" name="action" value="save_and_continue" class="btn btn-success me-2">
                                <i class="fas fa-plus me-2"></i>
                                حفظ وإضافة آخر
                            </button>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="saveDraft()">
                                <i class="fas fa-file-alt me-2"></i>
                                حفظ كمسودة
                            </button>
                            <a href="{{ route('admin.blog.index') }}" class="btn btn-outline-danger">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('scripts')
<script>
// Image preview functionality
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('previewImg').src = e.target.result;
            document.getElementById('imagePreview').style.display = 'block';
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function removeImage() {
    document.getElementById('image').value = '';
    document.getElementById('imagePreview').style.display = 'none';
}

// Meta description character counter
document.getElementById('meta_description').addEventListener('input', function() {
    const counter = document.getElementById('metaCounter');
    counter.textContent = this.value.length;
    
    if (this.value.length > 160) {
        counter.style.color = 'red';
    } else {
        counter.style.color = 'inherit';
    }
});

// Save as draft function
function saveDraft() {
    document.getElementById('status').value = 'draft';
    document.querySelector('form').submit();
}

// Auto-save functionality (every 2 minutes)
let autoSaveInterval;
function startAutoSave() {
    autoSaveInterval = setInterval(function() {
        // In a real application, you would save the form data to localStorage
        // or send an AJAX request to save as draft
        console.log('Auto-saving draft...');
        
        const formData = new FormData(document.querySelector('form'));
        localStorage.setItem('blog_draft', JSON.stringify(Object.fromEntries(formData)));
        
        // Show auto-save indicator
        showAutoSaveIndicator();
    }, 120000); // 2 minutes
}

function showAutoSaveIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'alert alert-info alert-dismissible fade show position-fixed';
    indicator.style.top = '20px';
    indicator.style.right = '20px';
    indicator.style.zIndex = '9999';
    indicator.innerHTML = `
        <i class="fas fa-save me-2"></i>
        تم حفظ المسودة تلقائياً
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(indicator);
    
    // Auto-remove after 3 seconds
    setTimeout(function() {
        if (indicator.parentNode) {
            indicator.remove();
        }
    }, 3000);
}

// Load draft from localStorage if exists
function loadDraft() {
    const draft = localStorage.getItem('blog_draft');
    if (draft && confirm('تم العثور على مسودة محفوظة. هل تريد استعادتها؟')) {
        const draftData = JSON.parse(draft);
        Object.keys(draftData).forEach(key => {
            const element = document.querySelector(`[name="${key}"]`);
            if (element) {
                element.value = draftData[key];
            }
        });
    }
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    startAutoSave();
    loadDraft();
    
    // Clear draft when form is successfully submitted
    document.querySelector('form').addEventListener('submit', function() {
        localStorage.removeItem('blog_draft');
        clearInterval(autoSaveInterval);
    });
});

// Warn user about unsaved changes
window.addEventListener('beforeunload', function(e) {
    const form = document.querySelector('form');
    const formData = new FormData(form);
    let hasContent = false;
    
    for (let [key, value] of formData.entries()) {
        if (value && value.toString().trim() !== '') {
            hasContent = true;
            break;
        }
    }
    
    if (hasContent) {
        e.preventDefault();
        e.returnValue = '';
    }
});
</script>
@endpush
