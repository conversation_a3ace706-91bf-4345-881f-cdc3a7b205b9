@extends('admin.layouts.app')

@section('title', 'عرض طلب العرض')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">عرض طلب العرض</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.quotes.index') }}">طلبات العروض</a></li>
                    <li class="breadcrumb-item active">عرض الطلب</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.quotes.index') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للقائمة
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Quote Details -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">تفاصيل طلب العرض</h6>
                    <div>
                        @switch($quote['status'])
                            @case('pending')
                                <span class="badge bg-warning">قيد المراجعة</span>
                                @break
                            @case('sent')
                                <span class="badge bg-info">تم الإرسال</span>
                                @break
                            @case('accepted')
                                <span class="badge bg-success">مقبول</span>
                                @break
                            @case('rejected')
                                <span class="badge bg-danger">مرفوض</span>
                                @break
                            @default
                                <span class="badge bg-secondary">غير محدد</span>
                        @endswitch
                    </div>
                </div>
                <div class="card-body">
                    <!-- Client Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted">اسم العميل</h6>
                            <p class="h5">{{ $quote['name'] }}</p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">البريد الإلكتروني</h6>
                            <p class="h5">
                                <a href="mailto:{{ $quote['email'] }}" class="text-decoration-none">
                                    {{ $quote['email'] }}
                                </a>
                            </p>
                        </div>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted">رقم الهاتف</h6>
                            <p class="h5">
                                @if(isset($quote['phone']) && $quote['phone'])
                                    <a href="tel:{{ $quote['phone'] }}" class="text-decoration-none">
                                        {{ $quote['phone'] }}
                                    </a>
                                @else
                                    <span class="text-muted">غير محدد</span>
                                @endif
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">الشركة</h6>
                            <p class="h5">
                                {{ $quote['company'] ?? 'غير محدد' }}
                            </p>
                        </div>
                    </div>

                    <!-- Service Details -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="text-muted">الخدمة المطلوبة</h6>
                            <p class="h5">
                                <span class="badge bg-info fs-6">{{ $quote['service'] }}</span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">الميزانية المتوقعة</h6>
                            <p class="h5 text-success">{{ $quote['budget'] }}</p>
                        </div>
                    </div>

                    <!-- Project Details -->
                    @if(isset($quote['project_description']) && $quote['project_description'])
                    <div class="mb-4">
                        <h6 class="text-muted">وصف المشروع</h6>
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0" style="white-space: pre-wrap;">{{ $quote['project_description'] }}</p>
                        </div>
                    </div>
                    @endif

                    @if(isset($quote['timeline']) && $quote['timeline'])
                    <div class="mb-4">
                        <h6 class="text-muted">الجدول الزمني المطلوب</h6>
                        <p>{{ $quote['timeline'] }}</p>
                    </div>
                    @endif

                    @if(isset($quote['additional_notes']) && $quote['additional_notes'])
                    <div class="mb-4">
                        <h6 class="text-muted">ملاحظات إضافية</h6>
                        <div class="bg-light p-3 rounded">
                            <p class="mb-0" style="white-space: pre-wrap;">{{ $quote['additional_notes'] }}</p>
                        </div>
                    </div>
                    @endif

                    <!-- Request Date -->
                    <div class="mb-4">
                        <h6 class="text-muted">تاريخ الطلب</h6>
                        <p class="h5">{{ date('Y/m/d H:i', strtotime($quote['created_at'])) }}</p>
                    </div>
                </div>
            </div>

            <!-- Quote Response Section -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">إرسال عرض السعر</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('admin.quotes.send', $quote['id']) }}" method="POST">
                        @csrf
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="quote_price" class="form-label">السعر المقترح (ر.س)</label>
                                <input type="number" class="form-control" id="quote_price" name="quote_price" 
                                       placeholder="مثال: 5000" required>
                            </div>
                            <div class="col-md-6">
                                <label for="delivery_time" class="form-label">مدة التسليم</label>
                                <select class="form-select" id="delivery_time" name="delivery_time" required>
                                    <option value="">اختر المدة</option>
                                    <option value="1-2 weeks">1-2 أسبوع</option>
                                    <option value="2-4 weeks">2-4 أسابيع</option>
                                    <option value="1-2 months">1-2 شهر</option>
                                    <option value="2-3 months">2-3 أشهر</option>
                                    <option value="custom">مدة مخصصة</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="quote_details" class="form-label">تفاصيل العرض</label>
                            <textarea class="form-control" id="quote_details" name="quote_details" 
                                      rows="6" required placeholder="اكتب تفاصيل العرض والخدمات المشمولة..."></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="terms_conditions" class="form-label">الشروط والأحكام</label>
                            <textarea class="form-control" id="terms_conditions" name="terms_conditions" 
                                      rows="4" placeholder="الشروط والأحكام الخاصة بالعرض..."></textarea>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-paper-plane me-2"></i>
                                إرسال العرض
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="openEmailClient()">
                                <i class="fas fa-external-link-alt me-2"></i>
                                فتح في برنامج البريد
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if($quote['status'] == 'pending')
                        <button onclick="updateStatus('sent')" class="btn btn-info">
                            <i class="fas fa-paper-plane me-2"></i>
                            تحديد كـ "تم الإرسال"
                        </button>
                        @endif
                        
                        <button onclick="updateStatus('accepted')" class="btn btn-success">
                            <i class="fas fa-check me-2"></i>
                            تحديد كـ "مقبول"
                        </button>
                        
                        <button onclick="updateStatus('rejected')" class="btn btn-warning">
                            <i class="fas fa-times me-2"></i>
                            تحديد كـ "مرفوض"
                        </button>
                        
                        <a href="mailto:{{ $quote['email'] }}" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-2"></i>
                            إرسال بريد إلكتروني
                        </a>
                        
                        @if(isset($quote['phone']) && $quote['phone'])
                        <a href="tel:{{ $quote['phone'] }}" class="btn btn-outline-success">
                            <i class="fas fa-phone me-2"></i>
                            اتصال هاتفي
                        </a>
                        @endif
                        
                        <button onclick="deleteQuote({{ $quote['id'] }})" class="btn btn-outline-danger">
                            <i class="fas fa-trash me-2"></i>
                            حذف الطلب
                        </button>
                    </div>
                </div>
            </div>

            <!-- Quote Timeline -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">سجل الطلب</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم استلام الطلب</h6>
                                <small class="text-muted">{{ date('Y/m/d H:i', strtotime($quote['created_at'])) }}</small>
                            </div>
                        </div>
                        
                        @if($quote['status'] != 'pending')
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم مراجعة الطلب</h6>
                                <small class="text-muted">{{ date('Y/m/d H:i') }}</small>
                            </div>
                        </div>
                        @endif
                        
                        @if($quote['status'] == 'sent')
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم إرسال العرض</h6>
                                <small class="text-muted">{{ date('Y/m/d H:i') }}</small>
                            </div>
                        </div>
                        @endif
                        
                        @if($quote['status'] == 'accepted')
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">تم قبول العرض</h6>
                                <small class="text-muted">{{ date('Y/m/d H:i') }}</small>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Client Stats -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">إحصائيات العميل</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary mb-1">1</h4>
                                <small class="text-muted">طلبات سابقة</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success mb-1">0</h4>
                            <small class="text-muted">مشاريع منجزة</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف طلب العرض هذا؟ لا يمكن التراجع عن هذا الإجراء.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">حذف</button>
            </div>
        </div>
    </div>
</div>

@endsection

@section('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 20px;
    width: 2px;
    height: calc(100% + 10px);
    background-color: #e3e6f0;
}

.timeline-marker {
    position: absolute;
    left: -26px;
    top: 4px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e3e6f0;
}

.timeline-content {
    padding-left: 10px;
}
</style>
@endsection

@section('scripts')
<script>
function updateStatus(status) {
    fetch(`/admin/quotes/{{ $quote['id'] }}/status`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status: status })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('حدث خطأ أثناء تحديث الحالة');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء تحديث الحالة');
    });
}

function deleteQuote(quoteId) {
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function openEmailClient() {
    const subject = encodeURIComponent('عرض سعر من وكالة التسويق');
    const body = encodeURIComponent(`مرحباً {{ $quote['name'] }},\n\nشكراً لاهتمامك بخدماتنا.\n\nنرفق لك عرض السعر المطلوب:\n\n`);
    const mailtoLink = `mailto:{{ $quote['email'] }}?subject=${subject}&body=${body}`;
    window.open(mailtoLink);
}

document.getElementById('confirmDelete').addEventListener('click', function() {
    fetch(`/admin/quotes/{{ $quote['id'] }}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = '{{ route('admin.quotes.index') }}';
        } else {
            alert('حدث خطأ أثناء حذف الطلب');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ أثناء حذف الطلب');
    });
    
    bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
});
</script>
@endsection
