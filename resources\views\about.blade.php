@extends('layouts.app')

@section('title', session('locale', 'ar') === 'ar' ? 'من نحن - Agent Marketing' : 'About Us - Agent Marketing')

@section('description', session('locale', 'ar') === 'ar' ? 'تعرف على قصة شركة Agent Marketing وفريق العمل المتخصص في التسويق الرقمي' : 'Learn about Agent Marketing\'s story and our specialized digital marketing team')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center" style="min-height: 60vh;">
            <div class="col-lg-6" data-aos="fade-right">
                <h1 class="display-4 fw-bold mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        من نحن؟
                    @else
                        Who We Are?
                    @endif
                </h1>
                <p class="lead mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        نحن فريق من المتخصصين المبدعين في مجال التسويق الرقمي، نساعد الشركات على تحقيق أهدافها وزيادة نموها من خلال استراتيجيات تسويقية مبتكرة ومدروسة
                    @else
                        We are a team of creative specialists in digital marketing, helping companies achieve their goals and accelerate growth through innovative and well-studied marketing strategies
                    @endif
                </p>
                <div class="d-flex align-items-center">
                    <div class="bg-primary text-white rounded-circle p-3 me-3">
                        <i class="fas fa-award fs-4"></i>
                    </div>
                    <div>
                        <strong>
                            {{ session('locale', 'ar') === 'ar' ? 'أكثر من 6 سنوات من الخبرة والتميز' : 'Over 6 Years of Experience and Excellence' }}
                        </strong>
                    </div>
                </div>
            </div>
            <div class="col-lg-6" data-aos="fade-left">
                <div class="text-center">
                    <i class="fas fa-users text-white" style="font-size: 8rem; opacity: 0.8;"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Company Stats -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">
                    @if(session('locale', 'ar') === 'ar')
                        إنجازاتنا بالأرقام
                    @else
                        Our Achievements in Numbers
                    @endif
                </h2>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-2 col-md-4 col-6 mb-4" data-aos="fade-up">
                <div class="stat-card text-center">
                    <div class="stat-icon bg-primary mb-3">
                        <i class="fas fa-calendar-alt text-white"></i>
                    </div>
                    <h3 class="stat-number">{{ $companyInfo['founded'] }}</h3>
                    <p class="stat-label">{{ session('locale', 'ar') === 'ar' ? 'سنة التأسيس' : 'Founded' }}</p>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="stat-card text-center">
                    <div class="stat-icon bg-success mb-3">
                        <i class="fas fa-users text-white"></i>
                    </div>
                    <h3 class="stat-number">{{ $companyInfo['clients'] }}</h3>
                    <p class="stat-label">{{ session('locale', 'ar') === 'ar' ? 'عميل راضي' : 'Happy Clients' }}</p>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="stat-card text-center">
                    <div class="stat-icon bg-warning mb-3">
                        <i class="fas fa-project-diagram text-white"></i>
                    </div>
                    <h3 class="stat-number">{{ $companyInfo['projects'] }}</h3>
                    <p class="stat-label">{{ session('locale', 'ar') === 'ar' ? 'مشروع مكتمل' : 'Completed Projects' }}</p>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-6 mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="stat-card text-center">
                    <div class="stat-icon bg-info mb-3">
                        <i class="fas fa-user-tie text-white"></i>
                    </div>
                    <h3 class="stat-number">{{ $companyInfo['team_size'] }}</h3>
                    <p class="stat-label">{{ session('locale', 'ar') === 'ar' ? 'عضو فريق' : 'Team Members' }}</p>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-6 mb-4" data-aos="fade-up" data-aos-delay="400">
                <div class="stat-card text-center">
                    <div class="stat-icon bg-danger mb-3">
                        <i class="fas fa-star text-white"></i>
                    </div>
                    <h3 class="stat-number">{{ $companyInfo['experience'] }}</h3>
                    <p class="stat-label">{{ session('locale', 'ar') === 'ar' ? 'سنوات خبرة' : 'Years Experience' }}</p>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-6 mb-4" data-aos="fade-up" data-aos-delay="500">
                <div class="stat-card text-center">
                    <div class="stat-icon bg-secondary mb-3">
                        <i class="fas fa-trophy text-white"></i>
                    </div>
                    <h3 class="stat-number">98%</h3>
                    <p class="stat-label">{{ session('locale', 'ar') === 'ar' ? 'معدل الرضا' : 'Satisfaction Rate' }}</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Our Story -->
<section class="section-padding">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6" data-aos="fade-right">
                <h2 class="section-title">
                    @if(session('locale', 'ar') === 'ar')
                        قصتنا
                    @else
                        Our Story
                    @endif
                </h2>
                <p class="mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        بدأت رحلتنا في عام 2018 بحلم بسيط: مساعدة الشركات الصغيرة والمتوسطة على النمو والازدهار في العالم الرقمي. منذ ذلك الحين، نمونا من فريق صغير مكون من 3 أشخاص إلى وكالة تسويق رقمي متكاملة تضم أكثر من 25 متخصص.
                    @else
                        Our journey began in 2018 with a simple dream: helping small and medium businesses grow and thrive in the digital world. Since then, we've grown from a small team of 3 people to a full-service digital marketing agency with over 25 specialists.
                    @endif
                </p>
                <p class="mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        نؤمن بأن كل عمل تجاري له قصة فريدة تستحق أن تُروى، ونحن هنا لنساعدك في إيصال قصتك إلى العالم بأفضل الطرق الممكنة. من خلال الجمع بين الإبداع والتكنولوجيا والاستراتيجية، نحقق نتائج استثنائية لعملائنا.
                    @else
                        We believe that every business has a unique story worth telling, and we're here to help you tell your story to the world in the best possible way. By combining creativity, technology, and strategy, we achieve exceptional results for our clients.
                    @endif
                </p>
                <div class="d-flex align-items-center">
                    <div class="bg-success text-white rounded-circle p-2 me-3">
                        <i class="fas fa-check"></i>
                    </div>
                    <span>{{ session('locale', 'ar') === 'ar' ? 'شراكة طويلة الأمد مع عملائنا' : 'Long-term partnership with our clients' }}</span>
                </div>
            </div>
            <div class="col-lg-6" data-aos="fade-left">
                <div class="timeline">
                    @foreach($milestones as $milestone)
                    <div class="timeline-item">
                        <div class="timeline-year">{{ $milestone['year'] }}</div>
                        <div class="timeline-content">
                            <h5>{{ $milestone['title'] }}</h5>
                            <p>{{ $milestone['description'] }}</p>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Our Values -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">
                    @if(session('locale', 'ar') === 'ar')
                        قيمنا ومبادئنا
                    @else
                        Our Values & Principles
                    @endif
                </h2>
                <p class="text-muted">
                    @if(session('locale', 'ar') === 'ar')
                        القيم التي نؤمن بها وتوجه عملنا اليومي
                    @else
                        The values we believe in and that guide our daily work
                    @endif
                </p>
            </div>
        </div>
        
        <div class="row">
            @foreach($values as $value)
            <div class="col-lg-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                <div class="value-card h-100">
                    <div class="d-flex align-items-start">
                        <div class="value-icon bg-{{ $value['color'] }} me-4">
                            <i class="{{ $value['icon'] }} text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-3">{{ $value['title'] }}</h5>
                            <p class="text-muted mb-0">{{ $value['description'] }}</p>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- Our Team -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">
                    @if(session('locale', 'ar') === 'ar')
                        تعرف على فريقنا
                    @else
                        Meet Our Team
                    @endif
                </h2>
                <p class="text-muted">
                    @if(session('locale', 'ar') === 'ar')
                        فريق من المتخصصين المبدعين والخبراء في مجالاتهم
                    @else
                        A team of creative specialists and experts in their fields
                    @endif
                </p>
            </div>
        </div>
        
        <div class="row">
            @foreach($team as $member)
            <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                <div class="team-card text-center">
                    <div class="team-image">
                        <div class="team-placeholder">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="team-social">
                            @foreach($member['social'] as $platform => $link)
                            <a href="{{ $link }}" class="social-link">
                                <i class="fab fa-{{ $platform }}"></i>
                            </a>
                            @endforeach
                        </div>
                    </div>
                    <div class="team-info">
                        <h5 class="team-name">{{ $member['name'] }}</h5>
                        <p class="team-position text-primary">{{ $member['position'] }}</p>
                        <p class="team-bio text-muted">{{ $member['bio'] }}</p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section-padding bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <h2 class="mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        هل أنت مستعد للانضمام إلى قصص نجاحنا؟
                    @else
                        Are You Ready to Join Our Success Stories?
                    @endif
                </h2>
                <p class="lead mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        دعنا نساعدك في كتابة قصة نجاحك التالية
                    @else
                        Let us help you write your next success story
                    @endif
                </p>
                <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
                    <a href="{{ route('quote') }}" class="btn btn-light btn-lg">
                        <i class="fas fa-rocket me-2"></i>
                        {{ session('locale', 'ar') === 'ar' ? 'ابدأ مشروعك الآن' : 'Start Your Project Now' }}
                    </a>
                    <a href="{{ route('contact') }}" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-comments me-2"></i>
                        {{ session('locale', 'ar') === 'ar' ? 'تحدث معنا' : 'Talk to Us' }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.stat-card {
    background: white;
    padding: 2rem 1rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 1.5rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #212529;
    margin: 1rem 0 0.5rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
}

.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #007bff;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -25px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #007bff;
}

.timeline-year {
    font-size: 1.2rem;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 0.5rem;
}

.timeline-content h5 {
    margin-bottom: 0.5rem;
}

.value-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.value-card:hover {
    transform: translateY(-5px);
}

.value-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.team-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    position: relative;
    overflow: hidden;
}

.team-card:hover {
    transform: translateY(-10px);
}

.team-image {
    position: relative;
    margin-bottom: 1.5rem;
}

.team-placeholder {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #007bff, #0056b3);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 3rem;
    color: white;
}

.team-social {
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 0.5rem;
}

.social-link {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #007bff;
    text-decoration: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #007bff;
    color: white;
    transform: translateY(-2px);
}

.team-name {
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.team-position {
    font-weight: 600;
    margin-bottom: 1rem;
}

.team-bio {
    font-size: 0.9rem;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .timeline {
        padding-left: 1rem;
    }
    
    .timeline::before {
        left: 8px;
    }
    
    .timeline-item::before {
        left: -18px;
    }
    
    .value-card {
        margin-bottom: 1rem;
    }
    
    .team-card {
        margin-bottom: 2rem;
    }
}
</style>
@endsection
