<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        $services = [
            [
                'title' => 'Ad Campaign Management',
                'description' => 'Professional management of your advertising campaigns on Facebook, Google, and Instagram',
                'icon' => 'fas fa-bullhorn',
                'slug' => 'ad-campaigns'
            ],
            [
                'title' => 'Social Media Management',
                'description' => 'Create and manage engaging content across social media platforms',
                'icon' => 'fas fa-share-alt',
                'slug' => 'social-media'
            ],
            [
                'title' => 'SEO Optimization',
                'description' => 'Optimize your website to rank higher in search results',
                'icon' => 'fas fa-search',
                'slug' => 'seo'
            ],
            [
                'title' => 'Brand Identity Design',
                'description' => 'Design unique logo and visual identity for your brand',
                'icon' => 'fas fa-palette',
                'slug' => 'branding'
            ]
        ];

        $stats = [
            ['number' => '200+', 'label_en' => 'Happy Clients'],
            ['number' => '500+', 'label_en' => 'Completed Projects'],
            ['number' => '5+', 'label_en' => 'Years Experience'],
            ['number' => '24/7', 'label_en' => 'Support']
        ];

        $testimonials = [
            [
                'name_en' => 'Ahmed <PERSON>',
                'company_en' => 'Asala Restaurant',
                'review_en' => 'Our sales increased by 300% after working with the marketing agency. Professional team and amazing results!',
                'rating' => 5,
                'image' => 'client1.jpg'
            ],
            [
                'name_en' => 'Fatima Ahmed',
                'company_en' => 'Elegance Store',
                'review_en' => 'Excellent service and creative design. We now have a strong visual identity that sets us apart from competitors.',
                'rating' => 5,
                'image' => 'client2.jpg'
            ]
        ];

        return view('home', compact('services', 'stats', 'testimonials'));
    }
}
