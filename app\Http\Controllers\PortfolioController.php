<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class PortfolioController extends Controller
{
    public function index()
    {
        $categories = [
            'all' => 'All Projects',
            'branding' => 'Branding',
            'web-development' => 'Web Development',
            'social-media' => 'Social Media',
            'advertising' => 'Advertising'
        ];

        $projects = [
            [
                'id' => 1,
                'title' => 'Asala Restaurant - Complete Brand Identity',
                'category' => 'branding',
                'client' => 'Asala Restaurant',
                'description' => 'Complete visual identity design for a traditional restaurant serving Saudi popular dishes',
                'image' => 'portfolio-1.jpg',
                'results' => [
                    '150% increase in brand awareness',
                    '80% increase in sales',
                    'Customer ratings improved to 4.8/5'
                ],
                'services' => ['branding', 'print-design', 'packaging'],
                'duration' => '3 months',
                'year' => '2023'
            ],
            [
                'id' => 2,
                'title' => 'Advanced Tech Company - Website',
                'category' => 'web-development',
                'client' => 'Advanced Tech Company',
                'description' => 'Development of responsive and fast website for a tech company specialized in smart solutions',
                'image' => 'portfolio-2.jpg',
                'results' => [
                    '200% improvement in loading speed',
                    '120% increase in organic traffic',
                    'Conversion rate improved to 12%'
                ],
                'services' => ['web-development', 'seo', 'ui-ux'],
                'duration' => '4 months',
                'year' => '2023'
            ],
            [
                'id' => 3,
                'title' => 'Modern Fashion Store - Ad Campaign',
                'category' => 'advertising',
                'client' => 'Modern Fashion Store',
                'description' => 'Comprehensive advertising campaign on Facebook and Instagram for a women\'s fashion store',
                'image' => 'portfolio-3.jpg',
                'results' => [
                    '300% increase in sales',
                    '60% reduction in customer acquisition cost',
                    'Reached 500,000 people'
                ],
                'services' => ['facebook-ads', 'instagram-ads', 'content-creation'],
                'duration' => '6 months',
                'year' => '2023'
            ],
            [
                'id' => 4,
                'title' => session('locale', 'ar') === 'ar' ? 'عيادة الأسنان المتخصصة - إدارة وسائل التواصل' : 'Specialized Dental Clinic - Social Media Management',
                'category' => 'social-media',
                'client' => session('locale', 'ar') === 'ar' ? 'عيادة الأسنان المتخصصة' : 'Specialized Dental Clinic',
                'description' => session('locale', 'ar') === 'ar' ? 'إدارة شاملة لحسابات وسائل التواصل الاجتماعي وإنشاء محتوى طبي تعليمي' : 'Comprehensive management of social media accounts and creation of educational medical content',
                'image' => 'portfolio-4.jpg',
                'results' => [
                    session('locale', 'ar') === 'ar' ? 'زيادة المتابعين بنسبة 400%' : '400% increase in followers',
                    session('locale', 'ar') === 'ar' ? 'زيادة الحجوزات بنسبة 150%' : '150% increase in bookings',
                    session('locale', 'ar') === 'ar' ? 'تحسن التفاعل بنسبة 250%' : '250% improvement in engagement'
                ],
                'services' => ['social-media-management', 'content-creation', 'community-management'],
                'duration' => session('locale', 'ar') === 'ar' ? '12 شهر' : '12 months',
                'year' => '2022-2023'
            ],
            [
                'id' => 5,
                'title' => session('locale', 'ar') === 'ar' ? 'شركة العقارات الذهبية - تحسين محركات البحث' : 'Golden Real Estate - SEO Optimization',
                'category' => 'web-development',
                'client' => session('locale', 'ar') === 'ar' ? 'شركة العقارات الذهبية' : 'Golden Real Estate',
                'description' => session('locale', 'ar') === 'ar' ? 'تحسين موقع شركة عقارية ليظهر في المراتب الأولى لكلمات البحث المهمة' : 'Optimizing a real estate company website to rank first for important search keywords',
                'image' => 'portfolio-5.jpg',
                'results' => [
                    session('locale', 'ar') === 'ar' ? 'الوصول للصفحة الأولى في جوجل' : 'Reached first page on Google',
                    session('locale', 'ar') === 'ar' ? 'زيادة الزيارات العضوية بنسبة 180%' : '180% increase in organic traffic',
                    session('locale', 'ar') === 'ar' ? 'زيادة الاستفسارات بنسبة 90%' : '90% increase in inquiries'
                ],
                'services' => ['seo', 'content-optimization', 'technical-seo'],
                'duration' => session('locale', 'ar') === 'ar' ? '8 أشهر' : '8 months',
                'year' => '2023'
            ],
            [
                'id' => 6,
                'title' => session('locale', 'ar') === 'ar' ? 'مخبز الحبوب الذهبية - هوية وتسويق' : 'Golden Grains Bakery - Branding & Marketing',
                'category' => 'branding',
                'client' => session('locale', 'ar') === 'ar' ? 'مخبز الحبوب الذهبية' : 'Golden Grains Bakery',
                'description' => session('locale', 'ar') === 'ar' ? 'تصميم هوية بصرية وحملة تسويقية متكاملة لمخبز تقليدي' : 'Visual identity design and integrated marketing campaign for a traditional bakery',
                'image' => 'portfolio-6.jpg',
                'results' => [
                    session('locale', 'ar') === 'ar' ? 'زيادة المبيعات بنسبة 120%' : '120% increase in sales',
                    session('locale', 'ar') === 'ar' ? 'افتتاح 3 فروع جديدة' : 'Opened 3 new branches',
                    session('locale', 'ar') === 'ar' ? 'تحسن الوعي بالعلامة التجارية بنسبة 200%' : '200% improvement in brand awareness'
                ],
                'services' => ['branding', 'social-media', 'print-design'],
                'duration' => session('locale', 'ar') === 'ar' ? '5 أشهر' : '5 months',
                'year' => '2022'
            ]
        ];

        return view('portfolio.index', compact('categories', 'projects'));
    }

    public function show($id)
    {
        // This would typically fetch from database
        // For now, we'll redirect to the portfolio index
        return redirect()->route('portfolio.index');
    }
}
