@extends('layouts.app')

@section('title', session('locale', 'ar') === 'ar' ? 'المدونة - Agent Marketing' : 'Blog - Agent Marketing')

@section('description', session('locale', 'ar') === 'ar' ? 'اكتشف أحدث النصائح والاستراتيجيات في التسويق الرقمي من خبراء Agent Marketing' : 'Discover the latest tips and strategies in digital marketing from Agent Marketing experts')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center" style="min-height: 60vh;">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <h1 class="display-4 fw-bold mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        مدونة التسويق الرقمي
                    @else
                        Digital Marketing Blog
                    @endif
                </h1>
                <p class="lead mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        اكتشف أحدث النصائح والاستراتيجيات والاتجاهات في عالم التسويق الرقمي من خبراء Agent Marketing
                    @else
                        Discover the latest tips, strategies, and trends in digital marketing from Agent Marketing experts
                    @endif
                </p>
                <div class="search-box" data-aos="fade-up" data-aos-delay="200">
                    <div class="input-group input-group-lg">
                        <span class="input-group-text bg-white border-end-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control border-start-0" id="blogSearch" 
                               placeholder="{{ session('locale', 'ar') === 'ar' ? 'ابحث في المقالات...' : 'Search articles...' }}">
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Articles -->
@php
    $featuredArticles = collect($articles)->where('featured', true)->take(2);
@endphp

@if($featuredArticles->count() > 0)
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">
                    @if(session('locale', 'ar') === 'ar')
                        المقالات المميزة
                    @else
                        Featured Articles
                    @endif
                </h2>
                <p class="text-muted">
                    @if(session('locale', 'ar') === 'ar')
                        أهم المقالات التي ننصح بقراءتها
                    @else
                        Top articles we recommend reading
                    @endif
                </p>
            </div>
        </div>
        
        <div class="row">
            @foreach($featuredArticles as $article)
            <div class="col-lg-6 mb-4" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                <div class="featured-article-card">
                    <div class="article-image">
                        <img src="{{ $article['image'] }}" alt="{{ $article['title'] }}" class="img-fluid">
                        <div class="article-badge">
                            <i class="fas fa-star me-1"></i>
                            {{ session('locale', 'ar') === 'ar' ? 'مميز' : 'Featured' }}
                        </div>
                        <div class="article-category">
                            {{ $categories[$article['category']] }}
                        </div>
                    </div>
                    <div class="article-content">
                        <div class="article-meta">
                            <span class="author">
                                <i class="fas fa-user me-1"></i>
                                {{ $article['author'] }}
                            </span>
                            <span class="date">
                                <i class="fas fa-calendar me-1"></i>
                                {{ date('M d, Y', strtotime($article['published_at'])) }}
                            </span>
                            <span class="read-time">
                                <i class="fas fa-clock me-1"></i>
                                {{ $article['read_time'] }} {{ session('locale', 'ar') === 'ar' ? 'دقائق' : 'min read' }}
                            </span>
                        </div>
                        <h3 class="article-title">
                            <a href="{{ route('blog.show', $article['slug']) }}">{{ $article['title'] }}</a>
                        </h3>
                        <p class="article-excerpt">{{ $article['excerpt'] }}</p>
                        <div class="article-tags">
                            @foreach($article['tags'] as $tag)
                            <span class="tag">#{{ $tag }}</span>
                            @endforeach
                        </div>
                        <a href="{{ route('blog.show', $article['slug']) }}" class="read-more-btn">
                            {{ session('locale', 'ar') === 'ar' ? 'اقرأ المزيد' : 'Read More' }}
                            <i class="fas fa-arrow-{{ session('locale', 'ar') === 'ar' ? 'left' : 'right' }} ms-2"></i>
                        </a>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- Blog Categories -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="blog-categories text-center mb-5" data-aos="fade-up">
                    <button class="category-btn active" data-category="all">
                        <i class="fas fa-th-large me-2"></i>
                        {{ session('locale', 'ar') === 'ar' ? 'جميع المقالات' : 'All Articles' }}
                    </button>
                    @foreach($categories as $key => $category)
                    <button class="category-btn" data-category="{{ $key }}">
                        <i class="fas fa-{{ $key === 'digital-marketing' ? 'chart-line' : ($key === 'social-media' ? 'share-alt' : ($key === 'seo' ? 'search' : ($key === 'content-marketing' ? 'edit' : ($key === 'advertising' ? 'bullhorn' : 'lightbulb')))) }} me-2"></i>
                        {{ $category }}
                    </button>
                    @endforeach
                </div>
            </div>
        </div>
        
        <div class="row" id="articlesGrid">
            @foreach($articles as $article)
            <div class="col-lg-4 col-md-6 mb-4 article-item" data-category="{{ $article['category'] }}" data-aos="fade-up">
                <div class="blog-card">
                    <div class="blog-image">
                        <img src="{{ $article['image'] }}" alt="{{ $article['title'] }}" class="img-fluid">
                        <div class="blog-category">{{ $categories[$article['category']] }}</div>
                        @if($article['featured'])
                        <div class="featured-badge">
                            <i class="fas fa-star"></i>
                        </div>
                        @endif
                    </div>
                    <div class="blog-content">
                        <div class="blog-meta">
                            <span class="author">
                                <i class="fas fa-user me-1"></i>
                                {{ $article['author'] }}
                            </span>
                            <span class="date">
                                <i class="fas fa-calendar me-1"></i>
                                {{ date('M d', strtotime($article['published_at'])) }}
                            </span>
                            <span class="read-time">
                                <i class="fas fa-clock me-1"></i>
                                {{ $article['read_time'] }}{{ session('locale', 'ar') === 'ar' ? 'د' : 'm' }}
                            </span>
                        </div>
                        <h4 class="blog-title">
                            <a href="{{ route('blog.show', $article['slug']) }}">{{ $article['title'] }}</a>
                        </h4>
                        <p class="blog-excerpt">{{ $article['excerpt'] }}</p>
                        <div class="blog-tags">
                            @foreach(array_slice($article['tags'], 0, 2) as $tag)
                            <span class="tag">#{{ $tag }}</span>
                            @endforeach
                        </div>
                        <div class="blog-footer">
                            <a href="{{ route('blog.show', $article['slug']) }}" class="read-more">
                                {{ session('locale', 'ar') === 'ar' ? 'اقرأ المزيد' : 'Read More' }}
                                <i class="fas fa-arrow-{{ session('locale', 'ar') === 'ar' ? 'left' : 'right' }} ms-1"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        <!-- No Results Message -->
        <div id="noResults" class="text-center py-5" style="display: none;">
            <i class="fas fa-search text-muted mb-3" style="font-size: 3rem;"></i>
            <h4 class="text-muted">
                {{ session('locale', 'ar') === 'ar' ? 'لم نجد أي مقالات' : 'No articles found' }}
            </h4>
            <p class="text-muted">
                {{ session('locale', 'ar') === 'ar' ? 'جرب البحث بكلمات مختلفة أو تصفح الفئات المختلفة' : 'Try searching with different keywords or browse different categories' }}
            </p>
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="section-padding bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <h2 class="mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        اشترك في نشرتنا الإخبارية
                    @else
                        Subscribe to Our Newsletter
                    @endif
                </h2>
                <p class="lead mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        احصل على أحدث المقالات والنصائح التسويقية مباشرة في بريدك الإلكتروني
                    @else
                        Get the latest articles and marketing tips delivered directly to your inbox
                    @endif
                </p>
                <div class="newsletter-form">
                    <div class="input-group input-group-lg">
                        <input type="email" class="form-control" 
                               placeholder="{{ session('locale', 'ar') === 'ar' ? 'أدخل بريدك الإلكتروني' : 'Enter your email address' }}">
                        <button class="btn btn-light" type="button">
                            <i class="fas fa-paper-plane me-2"></i>
                            {{ session('locale', 'ar') === 'ar' ? 'اشترك' : 'Subscribe' }}
                        </button>
                    </div>
                    <small class="text-light mt-2 d-block">
                        {{ session('locale', 'ar') === 'ar' ? 'لن نشارك بريدك الإلكتروني مع أي طرف ثالث' : 'We will never share your email with third parties' }}
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.search-box {
    max-width: 600px;
    margin: 0 auto;
}

.search-box .input-group-text {
    border-radius: 25px 0 0 25px;
}

.search-box .form-control {
    border-radius: 0 25px 25px 0;
    border-left: none;
}

.search-box .form-control:focus {
    box-shadow: none;
    border-color: #007bff;
}

.featured-article-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.featured-article-card:hover {
    transform: translateY(-10px);
}

.article-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.featured-article-card:hover .article-image img {
    transform: scale(1.1);
}

.article-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.8rem;
    font-weight: bold;
}

.article-category {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.8rem;
}

.article-content {
    padding: 2rem;
}

.article-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: #6c757d;
}

.article-title a {
    color: #212529;
    text-decoration: none;
    font-weight: bold;
    transition: color 0.3s ease;
}

.article-title a:hover {
    color: #007bff;
}

.article-excerpt {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.article-tags {
    margin-bottom: 1.5rem;
}

.tag {
    background: #e9ecef;
    color: #6c757d;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    display: inline-block;
}

.read-more-btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-block;
}

.read-more-btn:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.3);
}

.blog-categories {
    margin-bottom: 3rem;
}

.category-btn {
    background: white;
    border: 2px solid #e9ecef;
    color: #6c757d;
    padding: 0.75rem 1.5rem;
    margin: 0.25rem;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.category-btn:hover,
.category-btn.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
    transform: translateY(-2px);
}

.blog-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.blog-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.blog-card:hover .blog-image img {
    transform: scale(1.05);
}

.blog-category {
    position: absolute;
    top: 10px;
    left: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #ffd700;
    color: #333;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.blog-content {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.blog-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    margin-bottom: 1rem;
    font-size: 0.8rem;
    color: #6c757d;
}

.blog-title {
    margin-bottom: 1rem;
    flex-grow: 1;
}

.blog-title a {
    color: #212529;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.blog-title a:hover {
    color: #007bff;
}

.blog-excerpt {
    color: #6c757d;
    line-height: 1.5;
    margin-bottom: 1rem;
    flex-grow: 1;
}

.blog-tags {
    margin-bottom: 1rem;
}

.blog-footer {
    margin-top: auto;
}

.read-more {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.read-more:hover {
    color: #0056b3;
}

.newsletter-form {
    max-width: 500px;
    margin: 0 auto;
}

.newsletter-form .input-group-lg .form-control {
    border-radius: 25px 0 0 25px;
}

.newsletter-form .btn {
    border-radius: 0 25px 25px 0;
    font-weight: 500;
}

.article-item.hidden {
    display: none;
}

@media (max-width: 768px) {
    .article-meta,
    .blog-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .category-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        margin: 0.25rem 0.125rem;
    }
    
    .featured-article-card {
        margin-bottom: 2rem;
    }
    
    .newsletter-form .input-group {
        flex-direction: column;
    }
    
    .newsletter-form .form-control,
    .newsletter-form .btn {
        border-radius: 25px;
        margin-bottom: 0.5rem;
    }
}
</style>

<script>
// Blog functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('blogSearch');
    const categoryBtns = document.querySelectorAll('.category-btn');
    const articleItems = document.querySelectorAll('.article-item');
    const noResults = document.getElementById('noResults');
    
    // Search functionality
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        filterArticles(searchTerm, getActiveCategory());
    });
    
    // Category filtering
    categoryBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            categoryBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            const category = this.getAttribute('data-category');
            const searchTerm = searchInput.value.toLowerCase();
            filterArticles(searchTerm, category);
        });
    });
    
    function getActiveCategory() {
        const activeBtn = document.querySelector('.category-btn.active');
        return activeBtn ? activeBtn.getAttribute('data-category') : 'all';
    }
    
    function filterArticles(searchTerm, category) {
        let visibleCount = 0;
        
        articleItems.forEach(item => {
            const itemCategory = item.getAttribute('data-category');
            const titleText = item.querySelector('.blog-title a').textContent.toLowerCase();
            const excerptText = item.querySelector('.blog-excerpt').textContent.toLowerCase();
            
            const matchesSearch = searchTerm === '' || 
                                titleText.includes(searchTerm) || 
                                excerptText.includes(searchTerm);
            const matchesCategory = category === 'all' || itemCategory === category;
            
            if (matchesSearch && matchesCategory) {
                item.classList.remove('hidden');
                visibleCount++;
            } else {
                item.classList.add('hidden');
            }
        });
        
        // Show/hide no results message
        if (visibleCount === 0) {
            noResults.style.display = 'block';
        } else {
            noResults.style.display = 'none';
        }
    }
});
</script>
@endsection
