@extends('layouts.app')

@section('title', session('locale', 'ar') === 'ar' ? 'احصل على عرض سعر - Agent Marketing' : 'Get Quote - Agent Marketing')

@section('description', session('locale', 'ar') === 'ar' ? 'احصل على عرض سعر مخصص ومفصل لخدماتنا التسويقية خلال 24 ساعة' : 'Get a custom and detailed quote for our marketing services within 24 hours')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center" style="min-height: 60vh;">
            <div class="col-lg-6" data-aos="fade-right">
                <h1 class="display-4 fw-bold mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        احصل على عرض سعر مخصص
                    @else
                        Get a Custom Quote
                    @endif
                </h1>
                <p class="lead mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        أخبرنا عن مشروعك وسنرسل لك عرض سعر مفصل ومخصص خلال 24 ساعة مجاناً
                    @else
                        Tell us about your project and we'll send you a detailed custom quote within 24 hours for free
                    @endif
                </p>
                <div class="d-flex align-items-center">
                    <div class="bg-warning text-dark rounded-circle p-3 me-3">
                        <i class="fas fa-clock fs-4"></i>
                    </div>
                    <div>
                        <strong>
                            {{ session('locale', 'ar') === 'ar' ? 'رد سريع خلال 24 ساعة' : 'Quick Response Within 24 Hours' }}
                        </strong>
                    </div>
                </div>
            </div>
            <div class="col-lg-6" data-aos="fade-left">
                <div class="text-center">
                    <i class="fas fa-calculator text-white" style="font-size: 8rem; opacity: 0.8;"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Quote Form Section -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert" data-aos="fade-up">
                        <i class="fas fa-check-circle me-2"></i>
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif
                
                <div class="card shadow-lg border-0" data-aos="fade-up">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <h3 class="mb-0">
                            <i class="fas fa-file-invoice-dollar me-2"></i>
                            {{ session('locale', 'ar') === 'ar' ? 'طلب عرض سعر' : 'Quote Request Form' }}
                        </h3>
                    </div>
                    <div class="card-body p-5">
                        <form action="{{ route('quote.store') }}" method="POST">
                            @csrf
                            
                            <!-- Personal Information -->
                            <h5 class="mb-4 text-primary">
                                <i class="fas fa-user me-2"></i>
                                {{ session('locale', 'ar') === 'ar' ? 'المعلومات الشخصية' : 'Personal Information' }}
                            </h5>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">
                                        {{ session('locale', 'ar') === 'ar' ? 'الاسم الكامل' : 'Full Name' }} *
                                    </label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">
                                        {{ session('locale', 'ar') === 'ar' ? 'البريد الإلكتروني' : 'Email Address' }} *
                                    </label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email') }}" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">
                                        {{ session('locale', 'ar') === 'ar' ? 'رقم الهاتف' : 'Phone Number' }} *
                                    </label>
                                    <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" name="phone" value="{{ old('phone') }}" required>
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6 mb-4">
                                    <label for="company" class="form-label">
                                        {{ session('locale', 'ar') === 'ar' ? 'اسم الشركة' : 'Company Name' }} *
                                    </label>
                                    <input type="text" class="form-control @error('company') is-invalid @enderror" 
                                           id="company" name="company" value="{{ old('company') }}" required>
                                    @error('company')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <hr class="my-4">
                            
                            <!-- Project Information -->
                            <h5 class="mb-4 text-primary">
                                <i class="fas fa-project-diagram me-2"></i>
                                {{ session('locale', 'ar') === 'ar' ? 'معلومات المشروع' : 'Project Information' }}
                            </h5>
                            
                            <div class="mb-4">
                                <label class="form-label">
                                    {{ session('locale', 'ar') === 'ar' ? 'الخدمات المطلوبة' : 'Required Services' }} *
                                </label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" name="services[]" value="ad-campaigns" id="service1">
                                            <label class="form-check-label" for="service1">
                                                {{ session('locale', 'ar') === 'ar' ? 'إدارة الحملات الإعلانية' : 'Ad Campaign Management' }}
                                            </label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" name="services[]" value="social-media" id="service2">
                                            <label class="form-check-label" for="service2">
                                                {{ session('locale', 'ar') === 'ar' ? 'إدارة وسائل التواصل الاجتماعي' : 'Social Media Management' }}
                                            </label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" name="services[]" value="seo" id="service3">
                                            <label class="form-check-label" for="service3">
                                                {{ session('locale', 'ar') === 'ar' ? 'تحسين محركات البحث' : 'SEO Optimization' }}
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" name="services[]" value="branding" id="service4">
                                            <label class="form-check-label" for="service4">
                                                {{ session('locale', 'ar') === 'ar' ? 'تصميم الهوية البصرية' : 'Brand Identity Design' }}
                                            </label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" name="services[]" value="web-development" id="service5">
                                            <label class="form-check-label" for="service5">
                                                {{ session('locale', 'ar') === 'ar' ? 'تطوير المواقع الإلكترونية' : 'Web Development' }}
                                            </label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" name="services[]" value="content-marketing" id="service6">
                                            <label class="form-check-label" for="service6">
                                                {{ session('locale', 'ar') === 'ar' ? 'تسويق المحتوى' : 'Content Marketing' }}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                @error('services')
                                    <div class="text-danger small">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="budget" class="form-label">
                                        {{ session('locale', 'ar') === 'ar' ? 'الميزانية المتوقعة' : 'Expected Budget' }} *
                                    </label>
                                    <select class="form-select @error('budget') is-invalid @enderror" id="budget" name="budget" required>
                                        <option value="">
                                            {{ session('locale', 'ar') === 'ar' ? 'اختر الميزانية' : 'Select Budget' }}
                                        </option>
                                        <option value="5000-10000" {{ old('budget') == '5000-10000' ? 'selected' : '' }}>
                                            {{ session('locale', 'ar') === 'ar' ? '5,000 - 10,000 ريال' : '$1,300 - $2,700' }}
                                        </option>
                                        <option value="10000-25000" {{ old('budget') == '10000-25000' ? 'selected' : '' }}>
                                            {{ session('locale', 'ar') === 'ar' ? '10,000 - 25,000 ريال' : '$2,700 - $6,700' }}
                                        </option>
                                        <option value="25000-50000" {{ old('budget') == '25000-50000' ? 'selected' : '' }}>
                                            {{ session('locale', 'ar') === 'ar' ? '25,000 - 50,000 ريال' : '$6,700 - $13,300' }}
                                        </option>
                                        <option value="50000+" {{ old('budget') == '50000+' ? 'selected' : '' }}>
                                            {{ session('locale', 'ar') === 'ar' ? 'أكثر من 50,000 ريال' : '$13,300+' }}
                                        </option>
                                    </select>
                                    @error('budget')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="timeline" class="form-label">
                                        {{ session('locale', 'ar') === 'ar' ? 'الجدول الزمني المطلوب' : 'Required Timeline' }} *
                                    </label>
                                    <select class="form-select @error('timeline') is-invalid @enderror" id="timeline" name="timeline" required>
                                        <option value="">
                                            {{ session('locale', 'ar') === 'ar' ? 'اختر الجدول الزمني' : 'Select Timeline' }}
                                        </option>
                                        <option value="asap" {{ old('timeline') == 'asap' ? 'selected' : '' }}>
                                            {{ session('locale', 'ar') === 'ar' ? 'في أسرع وقت ممكن' : 'ASAP' }}
                                        </option>
                                        <option value="1-month" {{ old('timeline') == '1-month' ? 'selected' : '' }}>
                                            {{ session('locale', 'ar') === 'ar' ? 'خلال شهر' : 'Within 1 Month' }}
                                        </option>
                                        <option value="2-3-months" {{ old('timeline') == '2-3-months' ? 'selected' : '' }}>
                                            {{ session('locale', 'ar') === 'ar' ? '2-3 أشهر' : '2-3 Months' }}
                                        </option>
                                        <option value="3-6-months" {{ old('timeline') == '3-6-months' ? 'selected' : '' }}>
                                            {{ session('locale', 'ar') === 'ar' ? '3-6 أشهر' : '3-6 Months' }}
                                        </option>
                                        <option value="flexible" {{ old('timeline') == 'flexible' ? 'selected' : '' }}>
                                            {{ session('locale', 'ar') === 'ar' ? 'مرن' : 'Flexible' }}
                                        </option>
                                    </select>
                                    @error('timeline')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="description" class="form-label">
                                    {{ session('locale', 'ar') === 'ar' ? 'وصف المشروع والأهداف' : 'Project Description & Goals' }} *
                                </label>
                                <textarea class="form-control @error('description') is-invalid @enderror" 
                                          id="description" name="description" rows="5" required 
                                          placeholder="{{ session('locale', 'ar') === 'ar' ? 'أخبرنا عن مشروعك، أهدافك، جمهورك المستهدف، والنتائج المتوقعة...' : 'Tell us about your project, goals, target audience, and expected results...' }}">{{ old('description') }}</textarea>
                                @error('description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg px-5">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    {{ session('locale', 'ar') === 'ar' ? 'إرسال طلب العرض' : 'Send Quote Request' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Benefits Section -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center mb-5" data-aos="fade-up">
                <h2 class="section-title">
                    @if(session('locale', 'ar') === 'ar')
                        لماذا تطلب عرض سعر منا؟
                    @else
                        Why Request a Quote From Us?
                    @endif
                </h2>
            </div>
        </div>
        
        <div class="row">
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up">
                <div class="text-center">
                    <div class="service-icon bg-success mb-3">
                        <i class="fas fa-gift text-white"></i>
                    </div>
                    <h5>{{ session('locale', 'ar') === 'ar' ? 'مجاني تماماً' : 'Completely Free' }}</h5>
                    <p class="text-muted">
                        {{ session('locale', 'ar') === 'ar' ? 'لا نتقاضى أي رسوم مقابل إعداد عرض السعر المفصل' : 'We don\'t charge any fees for preparing a detailed quote' }}
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="text-center">
                    <div class="service-icon bg-warning mb-3">
                        <i class="fas fa-clock text-white"></i>
                    </div>
                    <h5>{{ session('locale', 'ar') === 'ar' ? 'رد سريع' : 'Quick Response' }}</h5>
                    <p class="text-muted">
                        {{ session('locale', 'ar') === 'ar' ? 'نرد على طلبك خلال 24 ساعة بعرض سعر مفصل' : 'We respond to your request within 24 hours with a detailed quote' }}
                    </p>
                </div>
            </div>
            
            <div class="col-lg-4 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="text-center">
                    <div class="service-icon bg-info mb-3">
                        <i class="fas fa-chart-line text-white"></i>
                    </div>
                    <h5>{{ session('locale', 'ar') === 'ar' ? 'استراتيجية مخصصة' : 'Custom Strategy' }}</h5>
                    <p class="text-muted">
                        {{ session('locale', 'ar') === 'ar' ? 'نضع استراتيجية مخصصة تناسب أهدافك وميزانيتك' : 'We create a custom strategy that fits your goals and budget' }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
