<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Session;

class AuthController extends Controller
{
    public function showLogin()
    {
        if (Session::get('admin_logged_in')) {
            return redirect()->route('admin.dashboard');
        }
        return view('admin.auth.login');
    }

    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|min:6'
        ]);

        // Simple authentication - in production, use proper user model
        $adminCredentials = [
            'email' => '<EMAIL>',
            'password' => 'admin123456' // In production, this should be hashed
        ];

        if ($request->email === $adminCredentials['email'] && 
            $request->password === $adminCredentials['password']) {
            
            Session::put('admin_logged_in', true);
            Session::put('admin_email', $request->email);
            Session::put('admin_name', 'Admin User');
            
            return redirect()->route('admin.dashboard')->with('success', 'Login successful');
        }

        return back()->withErrors([
            'email' => 'Invalid login credentials'
        ])->withInput();
    }

    public function logout()
    {
        Session::forget('admin_logged_in');
        Session::forget('admin_email');
        Session::forget('admin_name');
        
        return redirect()->route('admin.login')->with('success', 'Logout successful');
    }
}
