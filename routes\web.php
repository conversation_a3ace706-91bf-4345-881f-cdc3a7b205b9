<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\AboutController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\PortfolioController;
use App\Http\Controllers\PricingController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\TestimonialsController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\Admin\AuthController;
use App\Http\Controllers\Admin\AdminController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Marketing Agency Website Routes
| Professional marketing agency website with Arabic/English support
|
*/

// Main Pages
Route::get('/', [HomeController::class, 'index'])->name('home');
Route::get('/about', [AboutController::class, 'index'])->name('about');
Route::get('/services', [ServiceController::class, 'index'])->name('services');
Route::get('/services/{service}', [ServiceController::class, 'show'])->name('services.show');
Route::get('/portfolio', [PortfolioController::class, 'index'])->name('portfolio');
Route::get('/portfolio/{project}', [PortfolioController::class, 'show'])->name('portfolio.show');
Route::get('/pricing', [PricingController::class, 'index'])->name('pricing');
Route::get('/testimonials', [TestimonialsController::class, 'index'])->name('testimonials');
Route::get('/blog', [BlogController::class, 'index'])->name('blog');
Route::get('/blog/{post}', [BlogController::class, 'show'])->name('blog.show');
Route::get('/contact', [ContactController::class, 'index'])->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');
Route::get('/faq', function () { return view('faq'); })->name('faq');

// Quote Request
Route::get('/quote', function () { return view('quote'); })->name('quote');
Route::post('/quote', [ContactController::class, 'quote'])->name('quote.store');

// Language Switching
Route::get('/lang/{locale}', function ($locale) {
    if (in_array($locale, ['ar', 'en'])) {
        session(['locale' => $locale]);
    }
    return redirect()->back();
})->name('lang.switch');

/*
|--------------------------------------------------------------------------
| Admin Panel Routes
|--------------------------------------------------------------------------
|
| Admin authentication and management routes
| Protected by admin middleware for secure access
|
*/

// Admin Authentication Routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Login routes (accessible without authentication)
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [AuthController::class, 'login'])->name('login.post');
    
    // Protected admin routes (require authentication)
    Route::middleware('admin')->group(function () {
        // Dashboard
        Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard.index');
        
        // Blog Management
        Route::prefix('blog')->name('blog.')->group(function () {
            Route::get('/', [AdminController::class, 'blogIndex'])->name('index');
            Route::get('/create', [AdminController::class, 'blogCreate'])->name('create');
            Route::post('/', [AdminController::class, 'blogStore'])->name('store');
            Route::get('/{id}/edit', [AdminController::class, 'blogEdit'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'blogUpdate'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'blogDestroy'])->name('destroy');
        });
        
        // FAQ Management
        Route::prefix('faq')->name('faq.')->group(function () {
            Route::get('/', [AdminController::class, 'faqIndex'])->name('index');
            Route::get('/create', [AdminController::class, 'faqCreate'])->name('create');
            Route::post('/', [AdminController::class, 'faqStore'])->name('store');
            Route::get('/{id}/edit', [AdminController::class, 'faqEdit'])->name('edit');
            Route::put('/{id}', [AdminController::class, 'faqUpdate'])->name('update');
            Route::delete('/{id}', [AdminController::class, 'faqDestroy'])->name('destroy');
            Route::post('/bulk-action', [AdminController::class, 'faqBulkAction'])->name('bulk-action');
            Route::post('/{id}/toggle-status', [AdminController::class, 'faqToggleStatus'])->name('toggle-status');
        });
        
        // Contact Management
        Route::prefix('contacts')->name('contacts.')->group(function () {
            Route::get('/', [AdminController::class, 'contactsIndex'])->name('index');
            Route::get('/{id}', [AdminController::class, 'contactsShow'])->name('show');
            Route::delete('/{id}', [AdminController::class, 'contactsDestroy'])->name('destroy');
            Route::post('/{id}/mark-read', [AdminController::class, 'contactsMarkRead'])->name('mark-read');
        });
        
        // Quote Requests Management
        Route::prefix('quotes')->name('quotes.')->group(function () {
            Route::get('/', [AdminController::class, 'quotesIndex'])->name('index');
            Route::get('/{id}', [AdminController::class, 'quotesShow'])->name('show');
            Route::post('/{id}/send', [AdminController::class, 'quotesSend'])->name('send');
            Route::post('/{id}/status', [AdminController::class, 'quotesUpdateStatus'])->name('status');
            Route::delete('/{id}', [AdminController::class, 'quotesDestroy'])->name('destroy');
        });
        
        // Site Settings
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [AdminController::class, 'settings'])->name('index');
            Route::post('/', [AdminController::class, 'updateSettings'])->name('update');
        });
        
        // Logout
        Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
    });
});
