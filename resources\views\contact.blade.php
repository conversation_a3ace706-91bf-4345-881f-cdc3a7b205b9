@extends('layouts.app')

@section('title', session('locale', 'ar') === 'ar' ? 'اتصل بنا - Agent Marketing' : 'Contact Us - Agent Marketing')

@section('description', session('locale', 'ar') === 'ar' ? 'تواصل معنا للحصول على استشارة مجانية وعرض سعر مخصص لخدماتنا التسويقية' : 'Contact us for a free consultation and custom quote for our marketing services')

@section('content')
<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center" style="min-height: 60vh;">
            <div class="col-lg-6" data-aos="fade-right">
                <h1 class="display-4 fw-bold mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        تواصل معنا
                    @else
                        Contact Us
                    @endif
                </h1>
                <p class="lead mb-4">
                    @if(session('locale', 'ar') === 'ar')
                        نحن هنا لمساعدتك في تحقيق أهدافك التسويقية. تواصل معنا اليوم واحصل على استشارة مجانية.
                    @else
                        We're here to help you achieve your marketing goals. Contact us today and get a free consultation.
                    @endif
                </p>
            </div>
            <div class="col-lg-6" data-aos="fade-left">
                <div class="text-center">
                    <i class="fas fa-comments text-white" style="font-size: 8rem; opacity: 0.8;"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Info Section -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up">
                <div class="card text-center h-100 p-4">
                    <div class="service-icon bg-primary">
                        <i class="fas fa-phone text-white"></i>
                    </div>
                    <h5 class="card-title">
                        {{ session('locale', 'ar') === 'ar' ? 'اتصل بنا' : 'Call Us' }}
                    </h5>
                    <p class="card-text">{{ $contactInfo['phone'] }}</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="card text-center h-100 p-4">
                    <div class="service-icon bg-success">
                        <i class="fas fa-envelope text-white"></i>
                    </div>
                    <h5 class="card-title">
                        {{ session('locale', 'ar') === 'ar' ? 'راسلنا' : 'Email Us' }}
                    </h5>
                    <p class="card-text">{{ $contactInfo['email'] }}</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="card text-center h-100 p-4">
                    <div class="service-icon bg-warning">
                        <i class="fas fa-map-marker-alt text-white"></i>
                    </div>
                    <h5 class="card-title">
                        {{ session('locale', 'ar') === 'ar' ? 'موقعنا' : 'Our Location' }}
                    </h5>
                    <p class="card-text">{{ $contactInfo['address'] }}</p>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="card text-center h-100 p-4">
                    <div class="service-icon bg-info">
                        <i class="fas fa-clock text-white"></i>
                    </div>
                    <h5 class="card-title">
                        {{ session('locale', 'ar') === 'ar' ? 'ساعات العمل' : 'Working Hours' }}
                    </h5>
                    <p class="card-text">{{ $contactInfo['hours'] }}</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form Section -->
<section class="section-padding">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="text-center mb-5" data-aos="fade-up">
                    <h2 class="section-title">
                        @if(session('locale', 'ar') === 'ar')
                            أرسل لنا رسالة
                        @else
                            Send Us a Message
                        @endif
                    </h2>
                    <p class="section-subtitle">
                        @if(session('locale', 'ar') === 'ar')
                            املأ النموذج أدناه وسنتواصل معك خلال 24 ساعة
                        @else
                            Fill out the form below and we'll get back to you within 24 hours
                        @endif
                    </p>
                </div>
                
                @if(session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert" data-aos="fade-up">
                        <i class="fas fa-check-circle me-2"></i>
                        {{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif
                
                <div class="card shadow-lg border-0" data-aos="fade-up">
                    <div class="card-body p-5">
                        <form action="{{ route('contact.store') }}" method="POST">
                            @csrf
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="name" class="form-label">
                                        {{ session('locale', 'ar') === 'ar' ? 'الاسم الكامل' : 'Full Name' }} *
                                    </label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                           id="name" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">
                                        {{ session('locale', 'ar') === 'ar' ? 'البريد الإلكتروني' : 'Email Address' }} *
                                    </label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email') }}" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">
                                        {{ session('locale', 'ar') === 'ar' ? 'رقم الهاتف' : 'Phone Number' }} *
                                    </label>
                                    <input type="tel" class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" name="phone" value="{{ old('phone') }}" required>
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="company" class="form-label">
                                        {{ session('locale', 'ar') === 'ar' ? 'اسم الشركة' : 'Company Name' }}
                                    </label>
                                    <input type="text" class="form-control @error('company') is-invalid @enderror" 
                                           id="company" name="company" value="{{ old('company') }}">
                                    @error('company')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="service" class="form-label">
                                    {{ session('locale', 'ar') === 'ar' ? 'الخدمة المطلوبة' : 'Service Needed' }} *
                                </label>
                                <select class="form-select @error('service') is-invalid @enderror" 
                                        id="service" name="service" required>
                                    <option value="">
                                        {{ session('locale', 'ar') === 'ar' ? 'اختر الخدمة' : 'Select Service' }}
                                    </option>
                                    @foreach($services as $key => $service)
                                        <option value="{{ $key }}" {{ old('service') == $key ? 'selected' : '' }}>
                                            {{ $service }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('service')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="mb-4">
                                <label for="message" class="form-label">
                                    {{ session('locale', 'ar') === 'ar' ? 'الرسالة' : 'Message' }} *
                                </label>
                                <textarea class="form-control @error('message') is-invalid @enderror" 
                                          id="message" name="message" rows="5" required>{{ old('message') }}</textarea>
                                @error('message')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-paper-plane me-2"></i>
                                    {{ session('locale', 'ar') === 'ar' ? 'إرسال الرسالة' : 'Send Message' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="section-padding bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="text-center mb-5" data-aos="fade-up">
                    <h2 class="section-title">
                        @if(session('locale', 'ar') === 'ar')
                            الأسئلة الشائعة
                        @else
                            Frequently Asked Questions
                        @endif
                    </h2>
                </div>
                
                <div class="accordion" id="faqAccordion" data-aos="fade-up">
                    <div class="accordion-item mb-3">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                @if(session('locale', 'ar') === 'ar')
                                    كم يستغرق تنفيذ الحملة الإعلانية؟
                                @else
                                    How long does it take to execute an advertising campaign?
                                @endif
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                @if(session('locale', 'ar') === 'ar')
                                    عادة ما يستغرق إعداد وتشغيل الحملة الإعلانية من 3-7 أيام عمل، حسب تعقيد المشروع ومتطلباته.
                                @else
                                    Setting up and launching an advertising campaign typically takes 3-7 business days, depending on the project's complexity and requirements.
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item mb-3">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                @if(session('locale', 'ar') === 'ar')
                                    هل تقدمون ضماناً على النتائج؟
                                @else
                                    Do you provide a guarantee on results?
                                @endif
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                @if(session('locale', 'ar') === 'ar')
                                    نعم، نقدم ضمان استرداد الأموال إذا لم تحقق الحملة الأهداف المتفق عليها خلال الشهر الأول.
                                @else
                                    Yes, we offer a money-back guarantee if the campaign doesn't achieve the agreed-upon goals within the first month.
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item mb-3">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                @if(session('locale', 'ar') === 'ar')
                                    هل تعملون مع الشركات الصغيرة؟
                                @else
                                    Do you work with small businesses?
                                @endif
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                @if(session('locale', 'ar') === 'ar')
                                    بالطبع! نعمل مع الشركات من جميع الأحجام، من الشركات الناشئة إلى الشركات الكبيرة.
                                @else
                                    Absolutely! We work with businesses of all sizes, from startups to large corporations.
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                @if(session('locale', 'ar') === 'ar')
                                    كيف يتم الدفع؟
                                @else
                                    How is payment processed?
                                @endif
                            </button>
                        </h2>
                        <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                @if(session('locale', 'ar') === 'ar')
                                    نقبل الدفع عبر التحويل البنكي، الفيزا، الماستركارد، وأبل باي. يمكن الدفع شهرياً أو مقدماً.
                                @else
                                    We accept payment via bank transfer, Visa, Mastercard, and Apple Pay. Payment can be made monthly or in advance.
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="section-padding bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8" data-aos="fade-right">
                <h2 class="mb-3">
                    @if(session('locale', 'ar') === 'ar')
                        مستعد لبدء مشروعك؟
                    @else
                        Ready to Start Your Project?
                    @endif
                </h2>
                <p class="lead mb-0">
                    @if(session('locale', 'ar') === 'ar')
                        احصل على عرض سعر مخصص ومفصل لمشروعك خلال 24 ساعة
                    @else
                        Get a custom and detailed quote for your project within 24 hours
                    @endif
                </p>
            </div>
            <div class="col-lg-4 text-lg-end" data-aos="fade-left">
                <a href="{{ route('quote') }}" class="btn btn-warning btn-lg">
                    <i class="fas fa-calculator me-2"></i>
                    {{ session('locale', 'ar') === 'ar' ? 'احصل على عرض سعر' : 'Get Quote' }}
                </a>
            </div>
        </div>
    </div>
</section>
@endsection
