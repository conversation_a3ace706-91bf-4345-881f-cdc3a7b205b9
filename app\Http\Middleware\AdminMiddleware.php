<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class AdminMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if admin is logged in via session
        if (!session('admin_logged_in')) {
            // If this is an AJAX request, return JSON response
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Unauthorized',
                    'message' => 'يجب تسجيل الدخول كمدير للوصول لهذه الصفحة'
                ], 401);
            }
            
            // Store the intended URL for redirect after login
            session(['admin_intended_url' => $request->url()]);
            
            // Redirect to admin login with error message
            return redirect()->route('admin.login')
                ->with('error', 'يجب تسجيل الدخول كمدير للوصول لهذه الصفحة');
        }

        // Check if admin session is still valid (optional security check)
        $adminEmail = session('admin_email');
        $loginTime = session('admin_login_time');
        
        // Session timeout check (24 hours)
        if ($loginTime && (time() - $loginTime) > 86400) {
            // Clear admin session
            session()->forget(['admin_logged_in', 'admin_email', 'admin_name', 'admin_login_time']);
            
            return redirect()->route('admin.login')
                ->with('error', 'انتهت صلاحية جلسة العمل. يرجى تسجيل الدخول مرة أخرى');
        }

        // Add admin info to request for easy access in controllers
        $request->merge([
            'admin_email' => $adminEmail,
            'admin_name' => session('admin_name', 'المدير')
        ]);

        return $next($request);
    }
}
