@extends('admin.layout')

@section('title', 'Blog Management')
@section('page-title', 'Blog Management')

@section('content')
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                <li class="breadcrumb-item active">Blog Management</li>
            </ol>
        </nav>
    </div>
    <a href="{{ route('admin.blog.create') }}" class="btn btn-primary">
        <i class="fas fa-plus me-2"></i>
        Add New Article
    </a>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-blog fa-2x text-primary"></i>
                    </div>
                </div>
                <h4 class="fw-bold text-primary">{{ count($articles) }}</h4>
                <p class="text-muted mb-0">إجمالي المقالات</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="100">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <div class="bg-success bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                </div>
                <h4 class="fw-bold text-success">{{ collect($articles)->where('status', 'published')->count() }}</h4>
                <p class="text-muted mb-0">مقالات منشورة</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="200">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-edit fa-2x text-warning"></i>
                    </div>
                </div>
                <h4 class="fw-bold text-warning">{{ collect($articles)->where('status', 'draft')->count() }}</h4>
                <p class="text-muted mb-0">مسودات</p>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-3" data-aos="fade-up" data-aos-delay="300">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <div class="bg-info bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-eye fa-2x text-info"></i>
                    </div>
                </div>
                <h4 class="fw-bold text-info">{{ collect($articles)->sum('views') }}</h4>
                <p class="text-muted mb-0">إجمالي المشاهدات</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="card mb-4" data-aos="fade-up" data-aos-delay="400">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث في المقالات...">
                </div>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="categoryFilter">
                    <option value="">جميع الفئات</option>
                    <option value="digital-marketing">التسويق الرقمي</option>
                    <option value="social-media">وسائل التواصل الاجتماعي</option>
                    <option value="seo">تحسين محركات البحث</option>
                    <option value="content-marketing">تسويق المحتوى</option>
                    <option value="advertising">الإعلانات</option>
                    <option value="tips">نصائح وحيل</option>
                </select>
            </div>
            <div class="col-md-3">
                <select class="form-select" id="statusFilter">
                    <option value="">جميع الحالات</option>
                    <option value="published">منشور</option>
                    <option value="draft">مسودة</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Articles Table -->
<div class="card" data-aos="fade-up" data-aos-delay="500">
    <div class="card-header">
        <h5><i class="fas fa-list me-2"></i>قائمة المقالات</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="articlesTable">
                <thead>
                    <tr>
                        <th>العنوان</th>
                        <th>الفئة</th>
                        <th>الحالة</th>
                        <th>المشاهدات</th>
                        <th>مميز</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($articles as $article)
                    <tr class="article-row" 
                        data-category="{{ $article['category'] }}" 
                        data-status="{{ $article['status'] }}"
                        data-title="{{ strtolower($article['title']) }}">
                        <td>
                            <div class="d-flex align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ $article['title'] }}</h6>
                                    <small class="text-muted">ID: {{ $article['id'] }}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-primary">
                                @switch($article['category'])
                                    @case('digital-marketing')
                                        التسويق الرقمي
                                        @break
                                    @case('social-media')
                                        وسائل التواصل الاجتماعي
                                        @break
                                    @case('seo')
                                        تحسين محركات البحث
                                        @break
                                    @case('content-marketing')
                                        تسويق المحتوى
                                        @break
                                    @case('advertising')
                                        الإعلانات
                                        @break
                                    @case('tips')
                                        نصائح وحيل
                                        @break
                                    @default
                                        {{ $article['category'] }}
                                @endswitch
                            </span>
                        </td>
                        <td>
                            @if($article['status'] === 'published')
                                <span class="badge bg-success">منشور</span>
                            @else
                                <span class="badge bg-warning">مسودة</span>
                            @endif
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <i class="fas fa-eye text-muted me-1"></i>
                                {{ number_format($article['views']) }}
                            </div>
                        </td>
                        <td>
                            @if($article['featured'])
                                <i class="fas fa-star text-warning" title="مقال مميز"></i>
                            @else
                                <i class="far fa-star text-muted" title="غير مميز"></i>
                            @endif
                        </td>
                        <td>
                            <small class="text-muted">
                                {{ date('Y/m/d', strtotime($article['created_at'])) }}
                            </small>
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{{ route('admin.blog.edit', $article['id']) }}" 
                                   class="btn btn-sm btn-outline-primary" 
                                   title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" 
                                        class="btn btn-sm btn-outline-danger" 
                                        onclick="deleteArticle({{ $article['id'] }})"
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <a href="{{ route('blog.show', 'sample-slug-' . $article['id']) }}" 
                                   class="btn btn-sm btn-outline-info" 
                                   target="_blank"
                                   title="معاينة">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        
        @if(count($articles) === 0)
        <div class="text-center py-5">
            <i class="fas fa-blog fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد مقالات حالياً</h5>
            <p class="text-muted">ابدأ بإضافة مقال جديد لمدونتك</p>
            <a href="{{ route('admin.blog.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                إضافة مقال جديد
            </a>
        </div>
        @endif
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف هذا المقال؟ لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
// Search and Filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const categoryFilter = document.getElementById('categoryFilter');
    const statusFilter = document.getElementById('statusFilter');
    const articleRows = document.querySelectorAll('.article-row');
    
    function filterArticles() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedCategory = categoryFilter.value;
        const selectedStatus = statusFilter.value;
        
        articleRows.forEach(row => {
            const title = row.dataset.title;
            const category = row.dataset.category;
            const status = row.dataset.status;
            
            const matchesSearch = title.includes(searchTerm);
            const matchesCategory = !selectedCategory || category === selectedCategory;
            const matchesStatus = !selectedStatus || status === selectedStatus;
            
            if (matchesSearch && matchesCategory && matchesStatus) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
        
        // Update results count
        const visibleRows = Array.from(articleRows).filter(row => row.style.display !== 'none');
        updateResultsCount(visibleRows.length);
    }
    
    function updateResultsCount(count) {
        let resultsInfo = document.getElementById('resultsInfo');
        if (!resultsInfo) {
            resultsInfo = document.createElement('div');
            resultsInfo.id = 'resultsInfo';
            resultsInfo.className = 'text-muted mb-3';
            document.querySelector('#articlesTable').parentNode.insertBefore(resultsInfo, document.querySelector('#articlesTable'));
        }
        resultsInfo.textContent = `عرض ${count} من ${articleRows.length} مقال`;
    }
    
    searchInput.addEventListener('input', filterArticles);
    categoryFilter.addEventListener('change', filterArticles);
    statusFilter.addEventListener('change', filterArticles);
    
    // Initialize results count
    updateResultsCount(articleRows.length);
});

// Delete article function
function deleteArticle(articleId) {
    const deleteForm = document.getElementById('deleteForm');
    deleteForm.action = `/admin/blog/${articleId}`;
    
    const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}

// Auto-refresh page every 5 minutes to show updated view counts
setInterval(function() {
    // In a real application, you might want to use AJAX to update view counts
    // without refreshing the entire page
}, 300000);
</script>
@endpush
