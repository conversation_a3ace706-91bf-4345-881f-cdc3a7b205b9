@extends('layouts.app')

@section('title', 'Client Testimonials - Agent Marketing')
@section('meta_description', 'Read success stories from our satisfied clients. See how Agent Marketing has helped businesses achieve exceptional results with digital marketing, SEO, PPC, and social media campaigns.')

@push('meta_tags')
<meta property="og:title" content="Client Testimonials - Agent Marketing Success Stories">
<meta property="og:description" content="Discover how Agent Marketing has transformed businesses with proven digital marketing strategies. Real results from real clients.">
<meta property="og:type" content="website">
<meta property="og:url" content="{{ url()->current() }}">
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="Client Testimonials - Agent Marketing">
<meta name="twitter:description" content="Real success stories from businesses we've helped grow through strategic digital marketing.">
@endpush

@push('structured_data')
<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@type": "Organization",
  "name": "Agent Marketing",
  "aggregateRating": {
    "@type": "AggregateRating",
    "ratingValue": "{{ $stats['avg_rating'] }}",
    "reviewCount": "{{ $stats['total_clients'] }}",
    "bestRating": "5",
    "worstRating": "1"
  },
  "review": [
    @foreach($testimonials->take(3) as $index => $testimonial)
    {
      "@type": "Review",
      "author": {
        "@type": "Person",
        "name": "{{ $testimonial['name'] }}"
      },
      "reviewRating": {
        "@type": "Rating",
        "ratingValue": "{{ $testimonial['rating'] }}",
        "bestRating": "5"
      },
      "reviewBody": "{{ $testimonial['content'] }}"
    }{{ $loop->last ? '' : ',' }}
    @endforeach
  ]
}
</script>
@endpush

@section('content')
<!-- Hero Section -->
<section class="hero-section bg-gradient-primary text-white py-5">
    <div class="container">
        <div class="row align-items-center min-vh-50">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <h1 class="display-4 fw-bold mb-4">Client Success Stories</h1>
                <p class="lead mb-4">
                    Discover how we've helped businesses like yours achieve exceptional growth through strategic digital marketing. 
                    Real results from real clients who trusted us with their success.
                </p>
                <div class="row text-center mt-5">
                    <div class="col-md-3 col-6 mb-3">
                        <div class="stat-item">
                            <h3 class="fw-bold text-warning">{{ $stats['total_clients'] }}+</h3>
                            <p class="mb-0">Happy Clients</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <div class="stat-item">
                            <h3 class="fw-bold text-warning">{{ $stats['success_rate'] }}%</h3>
                            <p class="mb-0">Success Rate</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <div class="stat-item">
                            <h3 class="fw-bold text-warning">{{ $stats['avg_roi_increase'] }}%</h3>
                            <p class="mb-0">Avg ROI Increase</p>
                        </div>
                    </div>
                    <div class="col-md-3 col-6 mb-3">
                        <div class="stat-item">
                            <h3 class="fw-bold text-warning">{{ $stats['avg_rating'] }}</h3>
                            <p class="mb-0">Average Rating</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Filter Section -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="filter-tabs text-center" data-aos="fade-up">
                    <button class="btn btn-outline-primary me-2 mb-2 filter-btn active" data-filter="all">
                        All Testimonials
                    </button>
                    <button class="btn btn-outline-primary me-2 mb-2 filter-btn" data-filter="seo">
                        SEO Success
                    </button>
                    <button class="btn btn-outline-primary me-2 mb-2 filter-btn" data-filter="ppc">
                        PPC Results
                    </button>
                    <button class="btn btn-outline-primary me-2 mb-2 filter-btn" data-filter="social-media">
                        Social Media
                    </button>
                    <button class="btn btn-outline-primary me-2 mb-2 filter-btn" data-filter="content">
                        Content Marketing
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Testimonials -->
<section class="py-5">
    <div class="container">
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <h2 class="display-5 fw-bold mb-4">What Our Clients Say</h2>
                <p class="lead text-muted">
                    Don't just take our word for it. Here's what our clients have to say about working with Agent Marketing.
                </p>
            </div>
        </div>

        <!-- Featured Testimonials Carousel -->
        <div class="row mb-5">
            <div class="col-12" data-aos="fade-up" data-aos-delay="200">
                <div id="featuredTestimonials" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        @foreach($testimonials->where('featured', true)->values() as $index => $testimonial)
                        <div class="carousel-item {{ $index === 0 ? 'active' : '' }}">
                            <div class="row justify-content-center">
                                <div class="col-lg-8">
                                    <div class="testimonial-card-featured text-center p-5 bg-white rounded-4 shadow-lg">
                                        <div class="mb-4">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="fas fa-star text-warning {{ $i <= $testimonial['rating'] ? '' : 'opacity-25' }}"></i>
                                            @endfor
                                        </div>
                                        <blockquote class="blockquote mb-4">
                                            <p class="fs-5 text-dark">"{{ $testimonial['content'] }}"</p>
                                        </blockquote>
                                        <div class="d-flex align-items-center justify-content-center">
                                            <img src="{{ $testimonial['image'] }}" alt="{{ $testimonial['name'] }}" 
                                                 class="rounded-circle me-3" width="60" height="60"
                                                 onerror="this.src='https://via.placeholder.com/60x60/007bff/ffffff?text={{ substr($testimonial['name'], 0, 1) }}'">
                                            <div class="text-start">
                                                <h6 class="mb-0 fw-bold">{{ $testimonial['name'] }}</h6>
                                                <small class="text-muted">{{ $testimonial['position'] }}, {{ $testimonial['company'] }}</small>
                                            </div>
                                        </div>
                                        <div class="row mt-4 text-center">
                                            @foreach($testimonial['results'] as $metric => $value)
                                            <div class="col-4">
                                                <div class="result-metric">
                                                    <h5 class="text-primary fw-bold mb-0">{{ $value }}</h5>
                                                    <small class="text-muted text-capitalize">{{ str_replace('_', ' ', $metric) }}</small>
                                                </div>
                                            </div>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    <button class="carousel-control-prev" type="button" data-bs-target="#featuredTestimonials" data-bs-slide="prev">
                        <span class="carousel-control-prev-icon bg-primary rounded-circle p-3" aria-hidden="true"></span>
                        <span class="visually-hidden">Previous</span>
                    </button>
                    <button class="carousel-control-next" type="button" data-bs-target="#featuredTestimonials" data-bs-slide="next">
                        <span class="carousel-control-next-icon bg-primary rounded-circle p-3" aria-hidden="true"></span>
                        <span class="visually-hidden">Next</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- All Testimonials Grid -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row mb-5">
            <div class="col-lg-8 mx-auto text-center" data-aos="fade-up">
                <h2 class="display-6 fw-bold mb-4">All Client Reviews</h2>
                <p class="lead text-muted">
                    Browse through all our client testimonials and see the diverse range of businesses we've helped succeed.
                </p>
            </div>
        </div>

        <div class="row" id="testimonialsGrid">
            @foreach($testimonials as $testimonial)
            <div class="col-lg-4 col-md-6 mb-4 testimonial-item" 
                 data-category="{{ strtolower(str_replace(' ', '-', $testimonial['project_type'])) }}"
                 data-aos="fade-up" data-aos-delay="{{ ($loop->index % 3) * 100 }}">
                <div class="testimonial-card h-100 bg-white rounded-4 shadow-sm p-4 border-0">
                    <div class="d-flex align-items-center mb-3">
                        <img src="{{ $testimonial['image'] }}" alt="{{ $testimonial['name'] }}" 
                             class="rounded-circle me-3" width="50" height="50"
                             onerror="this.src='https://via.placeholder.com/50x50/007bff/ffffff?text={{ substr($testimonial['name'], 0, 1) }}'">
                        <div>
                            <h6 class="mb-0 fw-bold">{{ $testimonial['name'] }}</h6>
                            <small class="text-muted">{{ $testimonial['position'] }}</small>
                            <div class="company-name">
                                <small class="text-primary fw-semibold">{{ $testimonial['company'] }}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        @for($i = 1; $i <= 5; $i++)
                            <i class="fas fa-star text-warning {{ $i <= $testimonial['rating'] ? '' : 'opacity-25' }}"></i>
                        @endfor
                        <span class="badge bg-primary ms-2">{{ $testimonial['project_type'] }}</span>
                    </div>
                    
                    <blockquote class="mb-3">
                        <p class="text-dark">{{ $testimonial['content'] }}</p>
                    </blockquote>
                    
                    <div class="results-section mt-auto">
                        <h6 class="fw-bold text-success mb-2">Key Results:</h6>
                        <div class="row text-center">
                            @foreach($testimonial['results'] as $metric => $value)
                            <div class="col-4 mb-2">
                                <div class="result-badge">
                                    <div class="fw-bold text-success">{{ $value }}</div>
                                    <small class="text-muted text-capitalize">{{ str_replace('_', ' ', $metric) }}</small>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    
                    <div class="mt-3 pt-3 border-top">
                        <small class="text-muted">
                            <i class="fas fa-calendar me-1"></i>
                            {{ date('M Y', strtotime($testimonial['date'])) }}
                        </small>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="py-5 bg-gradient-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8" data-aos="fade-right">
                <h2 class="display-6 fw-bold mb-3">Ready to Join Our Success Stories?</h2>
                <p class="lead mb-4">
                    Let's create your success story next. Get a free consultation and discover how we can help your business achieve exceptional growth.
                </p>
            </div>
            <div class="col-lg-4 text-lg-end" data-aos="fade-left">
                <a href="{{ route('contact') }}" class="btn btn-warning btn-lg px-4 py-3 me-3 mb-2">
                    <i class="fas fa-comments me-2"></i>Get Free Consultation
                </a>
                <a href="{{ route('quote') }}" class="btn btn-outline-light btn-lg px-4 py-3 mb-2">
                    <i class="fas fa-calculator me-2"></i>Get Quote
                </a>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.min-vh-50 {
    min-height: 50vh;
}

.stat-item h3 {
    font-size: 2.5rem;
}

.filter-btn {
    transition: all 0.3s ease;
}

.filter-btn.active {
    background-color: var(--bs-primary);
    color: white;
    border-color: var(--bs-primary);
}

.testimonial-card-featured {
    border: 2px solid #f8f9fa;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial-card-featured:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 3rem rgba(0,0,0,.175)!important;
}

.testimonial-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid #e9ecef;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 2rem rgba(0,0,0,.15)!important;
    border-color: var(--bs-primary);
}

.result-metric h5 {
    font-size: 1.5rem;
}

.result-badge {
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 0.5rem;
    margin-bottom: 0.5rem;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    width: 3rem;
    height: 3rem;
}

.company-name {
    margin-top: 2px;
}

.results-section {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
}

@media (max-width: 768px) {
    .display-4 {
        font-size: 2.5rem;
    }
    
    .display-5 {
        font-size: 2rem;
    }
    
    .display-6 {
        font-size: 1.75rem;
    }
    
    .stat-item h3 {
        font-size: 2rem;
    }
    
    .testimonial-card-featured {
        padding: 2rem 1rem !important;
    }
    
    .filter-tabs {
        text-align: center;
    }
    
    .filter-btn {
        margin-bottom: 0.5rem;
        width: 100%;
        max-width: 200px;
    }
}
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality
    const filterBtns = document.querySelectorAll('.filter-btn');
    const testimonialItems = document.querySelectorAll('.testimonial-item');
    
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from all buttons
            filterBtns.forEach(b => b.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            
            const filter = this.getAttribute('data-filter');
            
            testimonialItems.forEach(item => {
                if (filter === 'all' || item.getAttribute('data-category').includes(filter)) {
                    item.style.display = 'block';
                    item.classList.add('fade-in');
                } else {
                    item.style.display = 'none';
                    item.classList.remove('fade-in');
                }
            });
        });
    });
    
    // Auto-play carousel with pause on hover
    const carousel = document.getElementById('featuredTestimonials');
    if (carousel) {
        const bsCarousel = new bootstrap.Carousel(carousel, {
            interval: 5000,
            wrap: true
        });
        
        carousel.addEventListener('mouseenter', () => {
            bsCarousel.pause();
        });
        
        carousel.addEventListener('mouseleave', () => {
            bsCarousel.cycle();
        });
    }
});
</script>
@endpush
