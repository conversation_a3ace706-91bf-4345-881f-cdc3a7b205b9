@extends('admin.layouts.app')

@section('title', 'تعديل المقال')
@section('page-title', 'تعديل المقال')

@section('content')
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">الرئيسية</a></li>
                <li class="breadcrumb-item"><a href="{{ route('admin.blog.index') }}">إدارة المدونة</a></li>
                <li class="breadcrumb-item active">تعديل المقال</li>
            </ol>
        </nav>
    </div>
    <div>
        <a href="{{ route('admin.blog.index') }}" class="btn btn-secondary me-2">
            <i class="fas fa-arrow-left me-2"></i>
            العودة للقائمة
        </a>
        <a href="{{ route('blog.show', 'sample-slug-' . $article['id']) }}" 
           class="btn btn-outline-info" 
           target="_blank">
            <i class="fas fa-eye me-2"></i>
            معاينة
        </a>
    </div>
</div>

<!-- Article Info -->
<div class="alert alert-info mb-4" data-aos="fade-up">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h6 class="mb-1">
                <i class="fas fa-info-circle me-2"></i>
                معلومات المقال
            </h6>
            <small class="text-muted">
                ID: {{ $article['id'] }} | 
                تاريخ الإنشاء: {{ date('Y/m/d H:i', strtotime($article['created_at'])) }} |
                المشاهدات: {{ number_format($article['views']) }}
            </small>
        </div>
        <div class="col-md-4 text-end">
            <span class="badge bg-{{ $article['status'] === 'published' ? 'success' : 'warning' }} me-2">
                {{ $article['status'] === 'published' ? 'منشور' : 'مسودة' }}
            </span>
            @if($article['featured'])
                <span class="badge bg-primary">مميز</span>
            @endif
        </div>
    </div>
</div>

<form method="POST" action="{{ route('admin.blog.update', $article['id']) }}" enctype="multipart/form-data">
    @csrf
    @method('PUT')
    
    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Basic Information -->
            <div class="card mb-4" data-aos="fade-up">
                <div class="card-header">
                    <h5><i class="fas fa-info-circle me-2"></i>المعلومات الأساسية</h5>
                </div>
                <div class="card-body">
                    <!-- Arabic Title -->
                    <div class="mb-3">
                        <label for="title_ar" class="form-label">
                            <i class="fas fa-heading me-1"></i>
                            العنوان (عربي) <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               class="form-control @error('title_ar') is-invalid @enderror" 
                               id="title_ar" 
                               name="title_ar" 
                               value="{{ old('title_ar', $article['title']) }}" 
                               required
                               placeholder="أدخل عنوان المقال باللغة العربية">
                        @error('title_ar')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- English Title -->
                    <div class="mb-3">
                        <label for="title_en" class="form-label">
                            <i class="fas fa-heading me-1"></i>
                            العنوان (إنجليزي) <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               class="form-control @error('title_en') is-invalid @enderror" 
                               id="title_en" 
                               name="title_en" 
                               value="{{ old('title_en', $article['title_en'] ?? '') }}" 
                               required
                               placeholder="Enter article title in English">
                        @error('title_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Arabic Excerpt -->
                    <div class="mb-3">
                        <label for="excerpt_ar" class="form-label">
                            <i class="fas fa-align-left me-1"></i>
                            المقدمة (عربي) <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control @error('excerpt_ar') is-invalid @enderror" 
                                  id="excerpt_ar" 
                                  name="excerpt_ar" 
                                  rows="3" 
                                  required
                                  placeholder="أدخل مقدمة المقال باللغة العربية">{{ old('excerpt_ar', $article['excerpt']) }}</textarea>
                        @error('excerpt_ar')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- English Excerpt -->
                    <div class="mb-3">
                        <label for="excerpt_en" class="form-label">
                            <i class="fas fa-align-left me-1"></i>
                            المقدمة (إنجليزي) <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control @error('excerpt_en') is-invalid @enderror" 
                                  id="excerpt_en" 
                                  name="excerpt_en" 
                                  rows="3" 
                                  required
                                  placeholder="Enter article excerpt in English">{{ old('excerpt_en', $article['excerpt_en'] ?? '') }}</textarea>
                        @error('excerpt_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Content -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="100">
                <div class="card-header">
                    <h5><i class="fas fa-file-alt me-2"></i>محتوى المقال</h5>
                </div>
                <div class="card-body">
                    <!-- Arabic Content -->
                    <div class="mb-3">
                        <label for="content_ar" class="form-label">
                            <i class="fas fa-paragraph me-1"></i>
                            المحتوى (عربي) <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control @error('content_ar') is-invalid @enderror" 
                                  id="content_ar" 
                                  name="content_ar" 
                                  rows="15" 
                                  required
                                  placeholder="أدخل محتوى المقال باللغة العربية">{{ old('content_ar', $article['content']) }}</textarea>
                        @error('content_ar')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- English Content -->
                    <div class="mb-3">
                        <label for="content_en" class="form-label">
                            <i class="fas fa-paragraph me-1"></i>
                            المحتوى (إنجليزي) <span class="text-danger">*</span>
                        </label>
                        <textarea class="form-control @error('content_en') is-invalid @enderror" 
                                  id="content_en" 
                                  name="content_en" 
                                  rows="15" 
                                  required
                                  placeholder="Enter article content in English">{{ old('content_en', $article['content_en'] ?? '') }}</textarea>
                        @error('content_en')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Revision History -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="200">
                <div class="card-header">
                    <h5><i class="fas fa-history me-2"></i>سجل التعديلات</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">إنشاء المقال</h6>
                                <p class="text-muted mb-1">تم إنشاء المقال لأول مرة</p>
                                <small class="text-muted">{{ date('Y/m/d H:i', strtotime($article['created_at'])) }}</small>
                            </div>
                        </div>
                        @if($article['updated_at'] !== $article['created_at'])
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">آخر تعديل</h6>
                                <p class="text-muted mb-1">تم تحديث محتوى المقال</p>
                                <small class="text-muted">{{ date('Y/m/d H:i', strtotime($article['updated_at'])) }}</small>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Publish Settings -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="300">
                <div class="card-header">
                    <h5><i class="fas fa-cog me-2"></i>إعدادات النشر</h5>
                </div>
                <div class="card-body">
                    <!-- Status -->
                    <div class="mb-3">
                        <label for="status" class="form-label">
                            <i class="fas fa-toggle-on me-1"></i>
                            حالة المقال
                        </label>
                        <select class="form-select" id="status" name="status">
                            <option value="draft" {{ old('status', $article['status']) === 'draft' ? 'selected' : '' }}>مسودة</option>
                            <option value="published" {{ old('status', $article['status']) === 'published' ? 'selected' : '' }}>منشور</option>
                        </select>
                    </div>
                    
                    <!-- Featured -->
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" 
                                   type="checkbox" 
                                   id="featured" 
                                   name="featured" 
                                   value="1"
                                   {{ old('featured', $article['featured']) ? 'checked' : '' }}>
                            <label class="form-check-label" for="featured">
                                <i class="fas fa-star me-1"></i>
                                مقال مميز
                            </label>
                        </div>
                        <small class="text-muted">سيظهر المقال في قسم المقالات المميزة</small>
                    </div>
                    
                    <!-- Publish Date -->
                    <div class="mb-3">
                        <label for="published_at" class="form-label">
                            <i class="fas fa-calendar me-1"></i>
                            تاريخ النشر
                        </label>
                        <input type="datetime-local" 
                               class="form-control" 
                               id="published_at" 
                               name="published_at" 
                               value="{{ old('published_at', date('Y-m-d\TH:i', strtotime($article['published_at'] ?? $article['created_at']))) }}">
                    </div>
                </div>
            </div>
            
            <!-- Category and Tags -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="400">
                <div class="card-header">
                    <h5><i class="fas fa-tags me-2"></i>التصنيف والعلامات</h5>
                </div>
                <div class="card-body">
                    <!-- Category -->
                    <div class="mb-3">
                        <label for="category" class="form-label">
                            <i class="fas fa-folder me-1"></i>
                            الفئة <span class="text-danger">*</span>
                        </label>
                        <select class="form-select @error('category') is-invalid @enderror" 
                                id="category" 
                                name="category" 
                                required>
                            <option value="">اختر الفئة</option>
                            @foreach($categories as $key => $value)
                                <option value="{{ $key }}" {{ old('category', $article['category']) === $key ? 'selected' : '' }}>
                                    {{ $value }}
                                </option>
                            @endforeach
                        </select>
                        @error('category')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Tags -->
                    <div class="mb-3">
                        <label for="tags" class="form-label">
                            <i class="fas fa-hashtag me-1"></i>
                            العلامات <span class="text-danger">*</span>
                        </label>
                        <input type="text" 
                               class="form-control @error('tags') is-invalid @enderror" 
                               id="tags" 
                               name="tags" 
                               value="{{ old('tags', implode(', ', $article['tags'])) }}" 
                               required
                               placeholder="مثال: تسويق، إعلانات، سوشيال ميديا">
                        <small class="text-muted">افصل بين العلامات بفاصلة</small>
                        @error('tags')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
            
            <!-- Featured Image -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="500">
                <div class="card-header">
                    <h5><i class="fas fa-image me-2"></i>الصورة المميزة</h5>
                </div>
                <div class="card-body">
                    <!-- Current Image -->
                    @if(isset($article['image']) && $article['image'])
                    <div class="mb-3">
                        <label class="form-label">الصورة الحالية:</label>
                        <div class="text-center">
                            <img src="{{ $article['image'] }}" 
                                 alt="الصورة الحالية" 
                                 class="img-fluid rounded" 
                                 style="max-height: 200px;">
                            <div class="mt-2">
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeCurrentImage()">
                                    <i class="fas fa-trash me-1"></i>
                                    إزالة الصورة الحالية
                                </button>
                            </div>
                        </div>
                    </div>
                    @endif
                    
                    <!-- New Image Upload -->
                    <div class="mb-3">
                        <label for="image" class="form-label">
                            {{ isset($article['image']) && $article['image'] ? 'تغيير الصورة:' : 'رفع صورة:' }}
                        </label>
                        <input type="file" 
                               class="form-control @error('image') is-invalid @enderror" 
                               id="image" 
                               name="image" 
                               accept="image/*"
                               onchange="previewImage(this)">
                        @error('image')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <small class="text-muted">الحد الأقصى: 2MB، الصيغ المدعومة: JPG, PNG, GIF</small>
                    </div>
                    
                    <!-- New Image Preview -->
                    <div id="imagePreview" class="text-center" style="display: none;">
                        <img id="previewImg" src="" alt="معاينة الصورة الجديدة" class="img-fluid rounded" style="max-height: 200px;">
                        <button type="button" class="btn btn-sm btn-outline-danger mt-2" onclick="removeNewImage()">
                            <i class="fas fa-trash me-1"></i>
                            إزالة الصورة الجديدة
                        </button>
                    </div>
                    
                    <!-- Hidden field to track image removal -->
                    <input type="hidden" id="remove_current_image" name="remove_current_image" value="0">
                </div>
            </div>
            
            <!-- SEO Settings -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="600">
                <div class="card-header">
                    <h5><i class="fas fa-search me-2"></i>إعدادات SEO</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="meta_description" class="form-label">
                            <i class="fas fa-file-text me-1"></i>
                            وصف الميتا
                        </label>
                        <textarea class="form-control" 
                                  id="meta_description" 
                                  name="meta_description" 
                                  rows="3" 
                                  maxlength="160"
                                  placeholder="وصف مختصر للمقال (160 حرف كحد أقصى)">{{ old('meta_description', $article['meta_description'] ?? '') }}</textarea>
                        <small class="text-muted">
                            <span id="metaCounter">{{ strlen(old('meta_description', $article['meta_description'] ?? '')) }}</span>/160 حرف
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="meta_keywords" class="form-label">
                            <i class="fas fa-key me-1"></i>
                            الكلمات المفتاحية
                        </label>
                        <input type="text" 
                               class="form-control" 
                               id="meta_keywords" 
                               name="meta_keywords" 
                               value="{{ old('meta_keywords', $article['meta_keywords'] ?? '') }}" 
                               placeholder="كلمات مفتاحية مفصولة بفاصلة">
                    </div>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="card mb-4" data-aos="fade-up" data-aos-delay="700">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar me-2"></i>إحصائيات المقال</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary mb-1">{{ number_format($article['views']) }}</h4>
                                <small class="text-muted">مشاهدة</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success mb-1">{{ $article['likes'] ?? 0 }}</h4>
                            <small class="text-muted">إعجاب</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="row">
        <div class="col-12">
            <div class="card" data-aos="fade-up" data-aos-delay="800">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <button type="submit" name="action" value="update" class="btn btn-primary me-2">
                                <i class="fas fa-save me-2"></i>
                                حفظ التعديلات
                            </button>
                            <button type="submit" name="action" value="update_and_continue" class="btn btn-success me-2">
                                <i class="fas fa-edit me-2"></i>
                                حفظ ومتابعة التعديل
                            </button>
                        </div>
                        <div>
                            <button type="button" class="btn btn-outline-secondary me-2" onclick="saveDraft()">
                                <i class="fas fa-file-alt me-2"></i>
                                حفظ كمسودة
                            </button>
                            <a href="{{ route('admin.blog.index') }}" class="btn btn-outline-danger">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #007bff;
}
</style>
@endpush

@push('scripts')
<script>
// Image preview functionality
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('previewImg').src = e.target.result;
            document.getElementById('imagePreview').style.display = 'block';
        };
        reader.readAsDataURL(input.files[0]);
    }
}

function removeNewImage() {
    document.getElementById('image').value = '';
    document.getElementById('imagePreview').style.display = 'none';
}

function removeCurrentImage() {
    document.getElementById('remove_current_image').value = '1';
    // Hide current image display
    const currentImageDiv = document.querySelector('.card-body .mb-3');
    if (currentImageDiv) {
        currentImageDiv.style.display = 'none';
    }
}

// Meta description character counter
document.getElementById('meta_description').addEventListener('input', function() {
    const counter = document.getElementById('metaCounter');
    counter.textContent = this.value.length;
    
    if (this.value.length > 160) {
        counter.style.color = 'red';
    } else {
        counter.style.color = 'inherit';
    }
});

// Save as draft function
function saveDraft() {
    document.getElementById('status').value = 'draft';
    document.querySelector('form').submit();
}

// Auto-save functionality (every 2 minutes)
let autoSaveInterval;
function startAutoSave() {
    autoSaveInterval = setInterval(function() {
        const formData = new FormData(document.querySelector('form'));
        localStorage.setItem('blog_edit_{{ $article["id"] }}', JSON.stringify(Object.fromEntries(formData)));
        showAutoSaveIndicator();
    }, 120000); // 2 minutes
}

function showAutoSaveIndicator() {
    const indicator = document.createElement('div');
    indicator.className = 'alert alert-info alert-dismissible fade show position-fixed';
    indicator.style.top = '20px';
    indicator.style.right = '20px';
    indicator.style.zIndex = '9999';
    indicator.innerHTML = `
        <i class="fas fa-save me-2"></i>
        تم حفظ التعديلات تلقائياً
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(indicator);
    
    setTimeout(function() {
        if (indicator.parentNode) {
            indicator.remove();
        }
    }, 3000);
}

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    startAutoSave();
    
    // Clear auto-save when form is successfully submitted
    document.querySelector('form').addEventListener('submit', function() {
        localStorage.removeItem('blog_edit_{{ $article["id"] }}');
        clearInterval(autoSaveInterval);
    });
});

// Warn user about unsaved changes
let formChanged = false;
document.querySelectorAll('input, textarea, select').forEach(element => {
    element.addEventListener('change', function() {
        formChanged = true;
    });
});

window.addEventListener('beforeunload', function(e) {
    if (formChanged) {
        e.preventDefault();
        e.returnValue = '';
    }
});

// Reset form changed flag when form is submitted
document.querySelector('form').addEventListener('submit', function() {
    formChanged = false;
});
</script>
@endpush
