<?php $__env->startSection('title', 'Main Dashboard'); ?>
<?php $__env->startSection('page-title', 'Main Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card" data-aos="fade-up">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h3 class="mb-2">Welcome, <?php echo e(Session::get('admin_name', 'Admin')); ?>!</h3>
                        <p class="text-muted mb-0">
                            Here's a quick overview of Agent Marketing's performance today
                        </p>
                    </div>
                    <div class="text-end">
                        <i class="fas fa-chart-line fa-3x text-primary opacity-25"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="100">
        <div class="card stats-card info position-relative">
            <i class="fas fa-cogs stats-icon"></i>
            <div class="stats-number"><?php echo e($stats['total_services']); ?></div>
            <div class="stats-label">Total Services</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="200">
        <div class="card stats-card success position-relative">
            <i class="fas fa-blog stats-icon"></i>
            <div class="stats-number"><?php echo e($stats['total_blog_posts']); ?></div>
            <div class="stats-label">Blog Posts</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="300">
        <div class="card stats-card warning position-relative">
            <i class="fas fa-question-circle stats-icon"></i>
            <div class="stats-number"><?php echo e($stats['total_faqs']); ?></div>
            <div class="stats-label">FAQ Items</div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="400">
        <div class="card stats-card danger position-relative">
            <i class="fas fa-star stats-icon"></i>
            <div class="stats-number"><?php echo e($stats['total_testimonials']); ?></div>
            <div class="stats-label">Testimonials</div>
        </div>
    </div>
</div>

<!-- Performance Metrics -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="500">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-users fa-2x text-primary"></i>
                    </div>
                </div>
                <h4 class="fw-bold text-primary"><?php echo e(number_format($stats['monthly_visitors'])); ?></h4>
                <p class="text-muted mb-0">Monthly Visitors</p>
                <small class="text-success">
                    <i class="fas fa-arrow-up me-1"></i>
                    +12% from last month
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="600">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-success bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-envelope fa-2x text-success"></i>
                    </div>
                </div>
                <h4 class="fw-bold text-success"><?php echo e($stats['contact_submissions']); ?></h4>
                <p class="text-muted mb-0">Contact Messages</p>
                <small class="text-success">
                    <i class="fas fa-arrow-up me-1"></i>
                    +8% from last week
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="700">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-file-invoice fa-2x text-warning"></i>
                    </div>
                </div>
                <h4 class="fw-bold text-warning"><?php echo e($stats['quote_requests']); ?></h4>
                <p class="text-muted mb-0">Quote Requests</p>
                <small class="text-success">
                    <i class="fas fa-arrow-up me-1"></i>
                    +15% from last week
                </small>
            </div>
        </div>
    </div>
    
    <div class="col-lg-3 col-md-6 mb-4" data-aos="fade-up" data-aos-delay="800">
        <div class="card text-center">
            <div class="card-body">
                <div class="d-flex align-items-center justify-content-center mb-3">
                    <div class="bg-info bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-newspaper fa-2x text-info"></i>
                    </div>
                </div>
                <h4 class="fw-bold text-info"><?php echo e($stats['newsletter_subscribers']); ?></h4>
                <p class="text-muted mb-0">Newsletter Subscribers</p>
                <small class="text-success">
                    <i class="fas fa-arrow-up me-1"></i>
                    +5% from last week
                </small>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities and Quick Actions -->
<div class="row">
    <!-- Recent Activities -->
    <div class="col-lg-8 mb-4">
        <div class="card" data-aos="fade-up" data-aos-delay="900">
            <div class="card-header">
                <h5><i class="fas fa-clock me-2"></i>Recent Activities</h5>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <?php $__currentLoopData = $recentActivities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="timeline-item d-flex align-items-start mb-3">
                        <div class="timeline-icon me-3">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-2">
                                <i class="<?php echo e($activity['icon']); ?> text-primary"></i>
                            </div>
                        </div>
                        <div class="timeline-content flex-grow-1">
                            <p class="mb-1"><?php echo e($activity['message']); ?></p>
                            <small class="text-muted"><?php echo e($activity['time']); ?></small>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                
                <div class="text-center mt-3">
                    <a href="<?php echo e(route('admin.dashboard')); ?>" class="btn btn-outline-primary">
                        <i class="fas fa-eye me-2"></i>
                        View All Activities
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card" data-aos="fade-up" data-aos-delay="1000">
            <div class="card-header">
                <h5><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-3">
                    <a href="<?php echo e(route('admin.blog.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        Add New Article
                    </a>
                    
                    <a href="<?php echo e(route('admin.faq.create')); ?>" class="btn btn-success">
                        <i class="fas fa-question me-2"></i>
                        Add FAQ Item
                    </a>
                    
                    <a href="<?php echo e(route('admin.contacts.index')); ?>" class="btn btn-info">
                        <i class="fas fa-envelope me-2"></i>
                        Review Messages
                    </a>
                    
                    <a href="<?php echo e(route('admin.quotes.index')); ?>" class="btn btn-warning">
                        <i class="fas fa-file-invoice me-2"></i>
                        Review Quote Requests
                    </a>
                    
                    <a href="<?php echo e(route('admin.settings.index')); ?>" class="btn btn-secondary">
                        <i class="fas fa-cog me-2"></i>
                        Site Settings
                    </a>
                </div>
            </div>
        </div>
        
        <!-- System Status -->
        <div class="card mt-4" data-aos="fade-up" data-aos-delay="1100">
            <div class="card-header">
                <h5><i class="fas fa-server me-2"></i>System Status</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Server Status</span>
                    <span class="badge bg-success">Online</span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Database</span>
                    <span class="badge bg-success">Running Normally</span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span>Storage Space</span>
                    <span class="badge bg-warning">75% Used</span>
                </div>
                
                <div class="d-flex justify-content-between align-items-center">
                    <span>Last Backup</span>
                    <span class="badge bg-info">2 Hours Ago</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Website Performance Chart -->
<div class="row">
    <div class="col-12">
        <div class="card" data-aos="fade-up" data-aos-delay="1200">
            <div class="card-header">
                <h5><i class="fas fa-chart-area me-2"></i>Website Performance - Last 7 Days</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-2 mb-3">
                        <div class="border rounded p-3">
                            <h6 class="text-muted mb-1">Sunday</h6>
                            <h4 class="text-primary mb-0">245</h4>
                            <small class="text-muted">visitors</small>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="border rounded p-3">
                            <h6 class="text-muted mb-1">Monday</h6>
                            <h4 class="text-primary mb-0">312</h4>
                            <small class="text-muted">visitors</small>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="border rounded p-3">
                            <h6 class="text-muted mb-1">Tuesday</h6>
                            <h4 class="text-primary mb-0">289</h4>
                            <small class="text-muted">visitors</small>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="border rounded p-3">
                            <h6 class="text-muted mb-1">Wednesday</h6>
                            <h4 class="text-primary mb-0">356</h4>
                            <small class="text-muted">visitors</small>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="border rounded p-3">
                            <h6 class="text-muted mb-1">Thursday</h6>
                            <h4 class="text-primary mb-0">423</h4>
                            <small class="text-muted">visitors</small>
                        </div>
                    </div>
                    <div class="col-md-2 mb-3">
                        <div class="border rounded p-3 bg-primary text-white">
                            <h6 class="mb-1">Today</h6>
                            <h4 class="mb-0">389</h4>
                            <small>visitors</small>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4 text-center">
                    <p class="text-muted mb-0">
                        <i class="fas fa-info-circle me-1"></i>
                        Total visitors this week: <strong>2,014 visitors</strong> 
                        (<span class="text-success">+18% from last week</span>)
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
.timeline-item {
    position: relative;
}

.timeline-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 20px;
    top: 40px;
    width: 2px;
    height: calc(100% + 12px);
    background: #e9ecef;
}

.stats-card {
    position: relative;
    overflow: hidden;
}

.stats-icon {
    position: absolute;
    top: 1rem;
    left: 1rem;
    opacity: 0.1;
}

.border {
    border: 2px solid #e9ecef !important;
    transition: all 0.3s ease;
}

.border:hover {
    border-color: #007bff !important;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,123,255,0.2);
}
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layout', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\market\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>